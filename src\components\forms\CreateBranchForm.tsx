import { useAppDispatch } from "@/hooks";
import {
  CreateBranchDef,
  CreateBranchSchema,
} from "@/models/validations/branch/create-branch.validation";
import { useCreateBranchMutation } from "@/services/branch.service";
import { useGetRegionsQuery } from "@/services/region.service";
import { useGetTaxJurisdictionsQuery } from "@/services/tax-jurisdiction.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { ComboBoxField, InputField, TextareaField } from "../form-fields";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateBranchForm = () => {
  const dispatch = useAppDispatch();

  const [createBranch, { isLoading: isCreatingEmployee }] =
    useCreateBranchMutation();

  const { data: taxJurisdictions, isLoading: isLoadingTaxJurisdictions } =
    useGetTaxJurisdictionsQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });

  const { data: regions, isLoading: isLoadingRegions } = useGetRegionsQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const form = useForm<CreateBranchDef>({
    resolver: zodResolver(CreateBranchSchema),
    defaultValues: {},
    mode: "all",
  });

  const onSubmit = async (data: CreateBranchDef) => {
    const res = await createBranch(data).unwrap();

    if (res.success) {
      toast.success("Create branch request submitted and is pending authorization");
      dispatch(modal.mutation.close());

      // toast(error)
    }
  };

  console.log(form.formState.errors);

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <>
              <InputField form={form} name="name" label="Name" />
              <ComboBoxField
                options={taxJurisdictions?.data || []}
                form={form}
                name="taxJurisdiction"
                label="Tax Jurisdiction"
                disabled={isLoadingTaxJurisdictions}
                isLoading={isLoadingTaxJurisdictions}
                placeholder="Select tax jurisdiction"
                labelKey="name"
                valueKey="name"
              />
              <ComboBoxField
                options={regions?.data || []}
                form={form}
                name="region"
                label="Region"
                disabled={isLoadingRegions}
                isLoading={isLoadingRegions}
                placeholder="Select region"
                labelKey="name"
                valueKey="name"
              />
              <TextareaField
                form={form}
                name="description"
                label="Description"
              />

              <div className="flex gap-4 justify-between">
                <Button className="font-medium" disabled={isCreatingEmployee}>
                  {isCreatingEmployee && (
                    <LoaderIcon className="animate-spin" />
                  )}
                  Create Branch/Unit
                </Button>
              </div>
            </>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default CreateBranchForm;
