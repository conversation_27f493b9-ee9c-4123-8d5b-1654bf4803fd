"use client";

import CreateCompanyForm from "../forms/CreateCompanyForm";
import { DialogDescription } from "../ui/dialog";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";
import { SheetHeader, SheetTitle } from "../ui/sheet";

const CreateCompanySheet = () => {
  return (
    <>
      <SheetHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <SheetTitle className="">Create Company</SheetTitle>
        <DialogDescription className="max-w-sm">{`Add new company`}</DialogDescription>
      </SheetHeader>
      <ScrollArea className="h-[calc(100vh-15vh)]">
        <CreateCompanyForm />
        <ScrollBar orientation="vertical" />
      </ScrollArea>
    </>
  );
};

export default CreateCompanySheet;
