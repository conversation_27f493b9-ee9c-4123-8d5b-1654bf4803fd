"use client";

import { useAppSelector } from "@/hooks";
import { BranchResponseDef } from "@/models/response/branch";
import UpdateBranchForm from "../forms/update/UpdateBranchForm";
import { DialogDescription } from "../ui/dialog";
import { She<PERSON><PERSON>eader, SheetTitle } from "../ui/sheet";

const UpdateBranchSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: BranchResponseDef };
  return (
    <>
      <SheetHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <SheetTitle className="">Update branch/location</SheetTitle>
        <DialogDescription className="max-w-sm">{`Update "${data.name}" branch/location`}</DialogDescription>
      </SheetHeader>
      <UpdateBranchForm />
    </>
  );
};

export default UpdateBranchSheet;
