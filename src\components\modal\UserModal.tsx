"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateUserForm from "../forms/CreateUserForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const UserModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new user</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateUserForm />
    </>
  );
};

export default UserModal;
