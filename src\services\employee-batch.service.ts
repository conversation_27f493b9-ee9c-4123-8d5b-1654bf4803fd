// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { EmpBatchResponseDef } from "@/models/response/emp-batch";
import { baseService } from "./base.service";

export const empBatchService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    getEmpBatch: builder.query<
      DefaultResponse & { data: EmpBatchResponseDef[] },
      void
    >({
      query: () => ({
        url: "/employee/batch/read",
        method: "GET",
      }),
      providesTags: ["EmpBatch"],
    }),

    getEmpBatchRecord: builder.query<
      DefaultResponse & { data: EmpBatchResponseDef[] },
      { id: string }
    >({
      query: (payload) => ({
        url: `/employee/batch-record/read/${payload.id}`,
        method: "GET",
      }),
      providesTags: (__, _, { id }) => [{ type: "EmpBatch", id }],
    }),
  }),
  overrideExisting: false,
});

export const { useGetEmpBatchQuery, useGetEmpBatchRecordQuery } =
  empBatchService;
