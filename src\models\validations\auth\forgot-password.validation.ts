import { z } from "zod";

export const ForgotPasswordSchema = z.object({
  otp: z.string().min(6, "Required"),
  email: z.string().email(),
  isRootUser: z.boolean(),
  user: z
    .object({
      password: z
        .string()
        .trim()
        .min(6, "Password must be at least 6 characters"),
      confirmPassword: z.string().trim(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    }),
});

export type ForgotPasswordDef = z.infer<typeof ForgotPasswordSchema>;
