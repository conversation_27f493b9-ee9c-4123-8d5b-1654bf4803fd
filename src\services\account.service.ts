// services/authApi.ts

import { AccountResponseDef } from "@/models/response/account/account.response";
import { DefaultResponse } from "@/models/response/default.response";
import { baseService } from "./base.service";

export const accountService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    getAccountData: builder.query<
      DefaultResponse & { data: AccountResponseDef },
      { slug: string }
    >({
      query: (credentials) => ({
        url: `/account/${credentials.slug}`,
        method: "GET",
      }),
      providesTags: ["Account"],
    }),
  }),
  overrideExisting: false,
});

export const { useGetAccountDataQuery } = accountService;
