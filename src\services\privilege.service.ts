// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { PrivilegeResponseDef } from "@/models/response/privilege";
import { baseService } from "./base.service";

export const privilegeService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    getPrivileges: builder.query<
      DefaultResponse & { data: PrivilegeResponseDef[] },
      void
    >({
      query: () => ({
        url: "/privilege",
        method: "GET",
      }),
      providesTags: ["Privileges"],
    }),
  }),
  overrideExisting: false,
});

export const { useGetPrivilegesQuery } = privilegeService;
