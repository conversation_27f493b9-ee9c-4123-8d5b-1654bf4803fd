import { RouteConstant } from "@/constants/RouteConstant";
import { useAppDispatch } from "@/hooks";
import {
  RootLoginDef,
  RootLoginSchema,
} from "@/models/validations/auth/login.validation";
import { useLoginMutation } from "@/services/auth.service";
import { auth } from "@/store/module/auth";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { Link } from "react-router-dom";
import { InputField } from "../form-fields";
import { Button } from "../ui/button";
import { Checkbox } from "../ui/checkbox";
import { Form } from "../ui/form";

const RootLoginForm = ({ cid }: { cid?: string }) => {
  const dispatch = useAppDispatch();

  const [login, { isLoading }] = useLoginMutation();

  const form = useForm<RootLoginDef>({
    resolver: zodResolver(RootLoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
    mode: "all",
  });

  // Reset email and password when the user type changes

  const onSubmit = async (data: RootLoginDef) => {
    const res = await login({
      ...data,
      companyId: cid!,
    }).unwrap();

    const {
      data: { email, roleId, userId },
    } = res;

    dispatch(auth.mutation.setToken(res.data.access_token));
    dispatch(
      auth.mutation.setAccount({
        userEmail: email,
        userId,
        userRoleId: roleId,
      })
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-4 bg-white shadow-md border rounded-md p-8"
      >
        <div className="space-y-4">
          <InputField
            form={form}
            name="email"
            label="Email"
            placeholder="Enter your email"
          />
          <InputField
            form={form}
            name="password"
            label="Password"
            type="password"
            placeholder="••••••••"
          />
        </div>

        <div className="my-6 flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <Checkbox id="terms" />
            <label
              htmlFor="terms"
              className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Remember me
            </label>
          </div>

          <Link
            to={`${RouteConstant.auth.forgetPassword}?cid=${cid}`}
            className="text-brand text-sm  font-medium"
          >
            Forgot password?
          </Link>
        </div>

        <Button className="w-full font-medium" disabled={isLoading}>
          {isLoading && <LoaderIcon className="animate-spin" />}
          Sign in
        </Button>
        <div>
          <p className="text-center text-sm">
            Don't have an account?{" "}
            <Link
              to={RouteConstant.auth.createAccount}
              className="font-semibold text-primary"
            >
              Create account
            </Link>
          </p>
        </div>
      </form>
    </Form>
  );
};

export default RootLoginForm;
