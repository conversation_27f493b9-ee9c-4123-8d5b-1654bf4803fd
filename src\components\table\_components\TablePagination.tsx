import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AgGridReact } from "ag-grid-react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";

interface PaginationContainerProps {
  gridRef: React.RefObject<AgGridReact | null>;
  isSsr?: boolean;
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  paginationPageSizeSelector: number[];
  setPaginationPageSize: (pageSize: number) => void;
}

const TablePagination = ({ isSsr, ...props }: PaginationContainerProps) => {
  const api = props?.gridRef.current?.api;
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [pageSize, setPageSize] = useState(10); // Default page size

  const actCurrentPage = isSsr ? props?.currentPage : currentPage;
  const actTotalPage = isSsr ? props?.totalPages : totalPages;

  // Update pagination state whenever it changes
  useEffect(() => {
    const updatePaginationState = () => {
      if (api) {
        setCurrentPage(api.paginationGetCurrentPage() + 1);
        setTotalPages(api.paginationGetTotalPages());
        setPageSize(api.paginationGetPageSize()); // Get the current page size
      }
    };

    if (!isSsr && api) {
      updatePaginationState();
      api.addEventListener("paginationChanged", updatePaginationState);

      return () => {
        api.removeEventListener("paginationChanged", updatePaginationState);
      };
    }
  }, [api, isSsr]);

  // Function to update the page size when the user selects a new one
  const handlePageSizeChange = (newPageSize: string) => {
    const newSize = parseInt(newPageSize, 10);
    props.setPaginationPageSize(newSize);
  };

  return (
    api && (
      <div className="flex items-center mt-5 gap-5 justify-between">
        <div className="flex items-center gap-3">
          <p className="text-sm text-neutral-500">Row per page:</p>
          <Select onValueChange={handlePageSizeChange}>
            <SelectTrigger className="w-max border-none shadow-none">
              <SelectValue placeholder={api?.paginationGetPageSize()} />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {props.paginationPageSizeSelector.map((pag) => (
                  <SelectItem
                    defaultChecked={pageSize === pag}
                    value={pag.toString()}
                  >
                    {pag}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
          <p>{`${actCurrentPage} - ${actTotalPage} of ${api?.paginationGetPageSize()}`}</p>
        </div>

        <div>
          <Button variant="ghost">
            <ChevronLeft />
          </Button>
          <Button variant="ghost">
            <ChevronRight />
          </Button>
        </div>
      </div>
    )
  );
};

export default TablePagination;
