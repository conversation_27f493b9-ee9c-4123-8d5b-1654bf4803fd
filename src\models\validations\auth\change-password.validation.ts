import { z } from "zod";

const passwordRequirements =
  /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()\-_=+{};:,<.>]).{6,}$/;

export const ChangePasswordSchema = z
  .object({
    oldPassword: z
      .string()
      .min(6, { message: "Password must be at least 6 characters long." }),
    newPassword: z
      .string()
      .min(6, { message: "Password must be at least 6 characters long." })
      .regex(passwordRequirements, {
        message:
          "Password must include at least 1 uppercase letter, 1 number, and 1 symbol.",
      }),
    confirmPassword: z.string().min(6, {
      message: "Confirm Password must be at least 6 characters long.",
    }),
  })
  .superRefine(({ newPassword, confirmPassword }, ctx) => {
    if (newPassword !== confirmPassword) {
      ctx.addIssue({
        code: "custom",
        path: ["userPassword"],
        message: "Passwords do not match.",
      });
      ctx.addIssue({
        code: "custom",
        path: ["confirmPassword"],
        message: "Passwords do not match.",
      });
    }
  });
export type ChangePasswordDef = z.infer<typeof ChangePasswordSchema>;
