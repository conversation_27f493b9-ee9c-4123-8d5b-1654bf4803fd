"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateDesignationForm from "../forms/CreateDesignationForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const DesignationModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new designation</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateDesignationForm />
    </>
  );
};

export default DesignationModal;
