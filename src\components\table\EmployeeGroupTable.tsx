import { DeleteEntityConstant } from "@/constants/DeleteEntityConstant";
import { ModalConstant } from "@/constants/ModalConstant";
import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { EmployeeResponseDef } from "@/models/response/employee";
import { GradeLevelResponseDef } from "@/models/response/grade-level";
import { useGetGradeLevelsQuery } from "@/services/employee-group.service";
import { modal } from "@/store/module/modal";
import { sheet } from "@/store/module/sheet";
import { ColDef, GridApi, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const EmployeeGroupTable = () => {
  const { data: employeeGroup, isLoading } = useGetGradeLevelsQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<GradeLevelResponseDef>[]>([
    {
      headerName: "Grade Level",
      field: "name",
    },
    {
      headerName: "Description",
      field: "description",
    },
  ]);

  const onEditClick = (node: IRowNode<EmployeeResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.updateGradeLevelSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };

  const onDeleteClick = async ({
    node,
  }: {
    node: IRowNode<GradeLevelResponseDef>;
    api: GridApi;
  }) => {
    if (!node.data) {
      toast.error("Grade not found");
    }
    dispatch(
      modal.mutation.open({
        open: true,
        modalType: ModalConstant.confirmationModal,
        metadata: {
          id: node.data?.id,
          entity: DeleteEntityConstant.gradeLevel,
          title: `Delete "${node.data?.name}" grade level?`,
          description: `Are you sure you want to delete this grade level "${node.data?.name}"`,
          warning:
            "By deleting this grade level, the associated record will lose access to this grade level.",
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={employeeGroup?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        defaultDeleteAction: onDeleteClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default EmployeeGroupTable;
