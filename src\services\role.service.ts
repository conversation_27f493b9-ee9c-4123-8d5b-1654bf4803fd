// services/authApi.ts

import { CreateRoleRequestDef } from "@/models/request/role/create-role.request";
import { UpdateRoleRequestDef } from "@/models/request/role/update-role.request";
import { DefaultResponse } from "@/models/response/default.response";
import {
  RoleDetailsResponseDef,
  RoleResponseDef,
} from "@/models/response/role";
import { baseService } from "./base.service";

export const roleService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createRole: builder.mutation<DefaultResponse, CreateRoleRequestDef>({
      query: (credentials) => ({
        url: "/role",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    updateRole: builder.mutation<DefaultResponse, UpdateRoleRequestDef>({
      query: (credentials) => ({
        url: "/role/update",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getRoles: builder.query<
      DefaultResponse & { data: RoleResponseDef[] },
      void
    >({
      query: () => ({
        url: "/role",
        method: "GET",
      }),
      providesTags: ["Roles"],
    }),

    getRoleById: builder.query<
      DefaultResponse & { data: RoleDetailsResponseDef },
      { roleId: number }
    >({
      query: (payload) => ({
        url: `/role/read/role-by-id/${payload.roleId}`,
        method: "GET",
      }),
      providesTags: (__, _, { roleId }) => [{ type: "Roles", id: roleId }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateRoleMutation,
  useGetRolesQuery,
  useGetRoleByIdQuery,
  useUpdateRoleMutation,
} = roleService;
