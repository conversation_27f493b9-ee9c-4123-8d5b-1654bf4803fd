import { ModalConstant } from "@/constants/ModalConstant";
import { useAppDispatch } from "@/hooks";
import { modal } from "@/store/module/modal";
import { PlusCircleIcon } from "lucide-react";
import { Button } from "../ui/button";

const TaxJurisdictionTableHeaderActions = () => {
  const dispatch = useAppDispatch();

  return (
    <div>
      <Button
        onClick={() => {
          dispatch(
            modal.mutation.open({
              modalType: ModalConstant.taxJurisdictionBulkUploadModal,
            })
          );
        }}
        size="sm"
        className="text-xs"
        variant="outline"
      >
        <PlusCircleIcon />
        Bulk Upload
      </Button>
    </div>
  );
};

export default TaxJurisdictionTableHeaderActions;
