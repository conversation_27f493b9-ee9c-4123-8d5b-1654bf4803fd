// services/authApi.ts

import { AuthorizationResponseDef } from "@/models/response/authorization";
import { DefaultResponse } from "@/models/response/default.response";
import { baseService } from "./base.service";

export const authorizationService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    acceptRequest: builder.mutation<DefaultResponse, { id: string }>({
      query: (credentials) => ({
        url: `/authorization/accept/${credentials.id}`,
        method: "PATCH",
        // body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    rejectRequest: builder.mutation<DefaultResponse, { id: string }>({
      query: (credentials) => ({
        url: `/authorization/reject/${credentials.id}`,
        method: "PATCH",
        // body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getAuthorizationRequest: builder.query<
      DefaultResponse & { data: AuthorizationResponseDef[] },
      void
    >({
      query: () => ({
        url: "/authorization",
        method: "GET",
      }),
      providesTags: ["Authorization"],
    }),
  }),
  overrideExisting: false,
});

export const { useAcceptRequestMutation, useRejectRequestMutation, useGetAuthorizationRequestQuery } =
  authorizationService;
