import { AccountResponseDef } from "@/models/response/account/account.response";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

type AccountState = {
  data: AccountResponseDef | null;
  isLoading: boolean;
  error: string | null;
};

const initialState: AccountState = {
  data: null,
  isLoading: false,
  error: null,
};

const slice = createSlice({
  name: "account",
  initialState,
  reducers: {
    setAccount: (state, action: PayloadAction<AccountResponseDef>) => {
      state.data = action.payload;
      state.error = null;
    },
    clearAccount: (state) => {
      state.data = null;
      state.error = null;
    },
    setAccountLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setAccountError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const account = {
  reducer: slice.reducer,
  mutation: slice.actions,
};
