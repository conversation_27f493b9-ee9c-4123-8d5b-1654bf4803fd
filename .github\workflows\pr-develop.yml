name: Deploy to UAT server

on:
  push:
    branches:
      - develop

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'
          
      - name: Install dependencies
        run: yarn install

      - name: Install Vite globally
        run: yarn global add vite

      - name: Build Vite application
        run: vite build

      - name: Clean directory
        uses: appleboy/ssh-action@v1.0.1 
        with:
          host: ${{ secrets.UAT_ONE_HOST }}
          username: ${{ secrets.UAT_ONE_USERNAME }}
          key: ${{ secrets.UAT_ONE_KEY }}
        
          script: |
            sudo rm -rf ${{ secrets.PAYROLL_TARGET }}/*
           
      - name: Deploy to server
        uses: appleboy/scp-action@v0.1.7 
        with:
          host: ${{ secrets.UAT_ONE_HOST }}             
          username: ${{ secrets.UAT_ONE_USERNAME }}     
          key: ${{ secrets.UAT_ONE_KEY }}           
          source: "dist/"                           
          target: ${{ secrets.PAYROLL_TARGET }}
          strip_components: 1
