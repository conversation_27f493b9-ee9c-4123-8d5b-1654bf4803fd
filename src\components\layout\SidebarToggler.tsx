"use client";
import { cn } from "@/lib/utils";
import { MenuIcon } from "lucide-react";
import { Button } from "../ui/button";
import { useSidebar } from "../ui/sidebar";

const SidebarToggler = ({
  Icon = MenuIcon,
  className,
}: {
  Icon?: React.JSX.ElementType;
  className?: string;
}) => {
  const { open, isMobile, toggleSidebar } = useSidebar();

  if (isMobile) return null;
  return (
    <Button
      className={cn(
        "fixed left-5 top-[47px] h-8 w-8 rounded-full z-50",
        className
      )}
      variant={open ? "default" : "ghost"}
      onClick={toggleSidebar}
    >
      <Icon className="w-5 h-5" />
    </Button>
  );
};

export default SidebarToggler;
