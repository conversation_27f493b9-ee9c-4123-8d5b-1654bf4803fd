// import packages

import { RouteConstant } from "@/constants/RouteConstant";
import { useAppSelector } from "@/hooks";
import { useLayoutEffect } from "react";
import { Outlet, useNavigate } from "react-router-dom";

// component
const ProtectedRoute = () => {
  const navigate = useNavigate();
  const { token } = useAppSelector((state) => state.auth); //add a dynamic method call

  useLayoutEffect(() => {
    if (!token) {
      navigate(RouteConstant.auth.login, { replace: true });
    }
  }, [token, navigate]);

  return <Outlet />;
};

export default ProtectedRoute;
