import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { countries } from "@/data/countries";
import { cn } from "@/lib/utils";
import parsePhoneNumberFromString, {
  AsYouType,
  type CarrierCode,
  type CountryCallingCode,
  type CountryCode,
  type E164Number,
  type NationalNumber,
  type NumberType,
} from "libphonenumber-js";
import { Check, ChevronsUpDown } from "lucide-react";
import { useRef, useState, useEffect } from "react";
import { useStateHistory } from "./use-state-history";

export type Country = (typeof countries)[number];

export type PhoneData = {
  phoneNumber?: E164Number;
  countryCode?: CountryCode;
  countryCallingCode?: CountryCallingCode;
  carrierCode?: CarrierCode;
  nationalNumber?: NationalNumber;
  internationalNumber?: string;
  possibleCountries?: string;
  isValid?: boolean;
  isPossible?: boolean;
  uri?: string;
  type?: NumberType;
};

interface PhoneInputProps extends React.ComponentPropsWithoutRef<"input"> {
  value?: string;
  defaultCountry?: CountryCode;
  triggerBtnclassName?: string;
  inputClassName?: string;
}

export function PhoneInput({
  value: valueProp,
  defaultCountry = "NG",
  className,
  id,
  required = true,
  inputClassName,
  triggerBtnclassName,
  onChange,
  ...rest
}: PhoneInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  const [openCommand, setOpenCommand] = useState(false);
  const [countryCode, setCountryCode] = useState<CountryCode>(defaultCountry);
  const [nationalNumber, setNationalNumber] = useState("");

  const selectedCountry = countries.find(
    (country) => country.iso2 === countryCode
  );

  // Initialize values from prop
  useEffect(() => {
    if (valueProp) {
      const parsedNumber = parsePhoneNumberFromString(valueProp);
      if (parsedNumber) {
        setCountryCode(parsedNumber.country || defaultCountry);
        setNationalNumber(parsedNumber.nationalNumber || "");
      } else {
        // If value starts with + and country code, extract the national number
        const phoneCodeRegex = /^\+(\d{1,4})\s?(.*)$/;
        const match = valueProp.match(phoneCodeRegex);
        if (match) {
          const [, code, number] = match;
          const country = countries.find(c => c.phone_code === code);
          if (country) {
            setCountryCode(country.iso2 as CountryCode);
            setNationalNumber(number);
          }
        }
      }
    }
  }, [valueProp, defaultCountry]);

  const handleNationalNumberChange = (event: React.FormEvent<HTMLInputElement>) => {
    const inputValue = event.currentTarget.value;
    // Remove any non-digit characters except spaces and dashes for formatting
    const cleanedValue = inputValue.replace(/[^\d\s-]/g, '');
    setNationalNumber(cleanedValue);
    
    // Combine country code with national number for the full value
    const fullPhoneNumber = `+${selectedCountry?.phone_code} ${cleanedValue}`;
    
    // Call the onChange handler with the full phone number
    if (onChange) {
      const syntheticEvent = {
        ...event,
        target: {
          ...event.target,
          value: fullPhoneNumber,
        },
        currentTarget: {
          ...event.currentTarget,
          value: fullPhoneNumber,
        },
      };
      onChange(syntheticEvent as React.ChangeEvent<HTMLInputElement>);
    }
  };

  const handleCountrySelect = (country: Country) => {
    setCountryCode(country.iso2 as CountryCode);
    
    // Update the full phone number with new country code
    const fullPhoneNumber = `+${country.phone_code} ${nationalNumber}`;
    
    // Call onChange to update the form
    if (onChange && inputRef.current) {
      const syntheticEvent = {
        target: {
          value: fullPhoneNumber,
        },
        currentTarget: {
          value: fullPhoneNumber,
        },
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(syntheticEvent);
    }
    
    setOpenCommand(false);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <div className={cn("flex gap-0", className)}>
      <Popover open={openCommand} onOpenChange={setOpenCommand} modal={true}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={openCommand}
            className={cn(
              "rounded-r-none border-r-0 w-max items-center justify-between whitespace-nowrap py-5",
              triggerBtnclassName
            )}
          >
            {selectedCountry?.name ? (
              <span className="text-sm font-medium">+{selectedCountry.phone_code}</span>
            ) : (
              "Select country"
            )}
            <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0 w-max" align="start">
          <Command>
            <CommandInput placeholder="Search country..." />
            <CommandList>
              <CommandEmpty>No country found.</CommandEmpty>
              <ScrollArea
                className={
                  "[&>[data-radix-scroll-area-viewport]]:max-h-[300px]"
                }
              >
                <CommandGroup>
                  {countries.map((country) => {
                    return (
                      <CommandItem
                        key={country.iso3}
                        value={`${country.name} (+${country.phone_code})`}
                        onSelect={() => handleCountrySelect(country)}
                      >
                        <Check
                          className={cn(
                            "mr-2 size-4",
                            countryCode === country.iso2
                              ? "opacity-100"
                              : "opacity-0"
                          )}
                        />
                        <span>{country.emoji}</span>
                        {country.name}
                        <span className="text-gray-11 ml-1">
                          (+{country.phone_code})
                        </span>
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              </ScrollArea>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      <Input
        ref={inputRef}
        type="text"
        pattern="^[0-9\s-]*$"
        name="phone"
        id={id}
        placeholder="Phone number"
        value={nationalNumber}
        onInput={handleNationalNumberChange}
        required={required}
        aria-required={required}
        maxLength={15}
        className={cn("py-5 rounded-l-none", inputClassName)}
        {...rest}
      />
    </div>
  );
}
