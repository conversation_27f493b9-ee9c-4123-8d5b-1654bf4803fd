export type CreateSalaryPackageRequestDef = {
  name: string;
  description?: string;
  baseSalary: number;

  pensionRate: number;
  nhfRate: number;
  taxAmount: number;
  currency: string;

  apprenticeAllowance: number;
  housingAllowance: number;
  transportAllowance: number;
  utilityAllowance: number;
  selfMaintenanceAllowance: number;
  hazardOrEntertainmentAllowance: number;
  furnitureAllowance: number;
  fuelSubsidy: number;
  domesticStaffAllowance: number;
  childEducationSubsidy: number;
  levelProficiencyAllowance: number;
  responsibilityAllowance: number;
  monthlyGrossSalary: number;
  annualGrossSalary: number;

  allowances?: {
    name: string;
    amount: number;
    id: string;
  }[];

  deductions?: {
    name: string;
    amount: number;
    id: string;
  }[];
};
