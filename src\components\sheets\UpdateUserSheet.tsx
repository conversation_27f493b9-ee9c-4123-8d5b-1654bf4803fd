"use client";

import { useAppSelector } from "@/hooks";
import { UserResponseDef } from "@/models/response/users";
import UpdateUserForm from "../forms/update/UpdateUserForm";
import { DialogDescription } from "../ui/dialog";
import { She<PERSON><PERSON>eader, SheetTitle } from "../ui/sheet";

const UpdateUserSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: UserResponseDef };
  return (
    <>
      <SheetHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <SheetTitle className="">Update user</SheetTitle>
        <DialogDescription className="max-w-sm">{`Update user - "${
          data.name || data.email
        }"`}</DialogDescription>
      </SheetHeader>
      <UpdateUserForm />
    </>
  );
};

export default UpdateUserSheet;
