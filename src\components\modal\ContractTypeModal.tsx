"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateContractTypeForm from "../forms/CreateContractTypeForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const ContractTypeModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new employment type</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateContractTypeForm />
    </>
  );
};

export default ContractTypeModal;
