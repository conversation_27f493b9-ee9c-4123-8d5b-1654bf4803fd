import { DashboardLayoutProps } from "@/view/layout/DashboardLayout";
import { authRoute } from "./auth.route";
import { dashboardRoute } from "./dasboard.route";

export type RouteDef = {
  path: string;
  Component: React.ComponentType;
  metadata?: RouteOptions;
};

type RouteOptions = {
  hasSidebar?: boolean;
  isAuthenticated?: boolean;
  redirectTo?: string;
  dashboardProps?: DashboardLayoutProps;
  isAlwaysPublic?: boolean;
};

const initialRoute = [] as RouteDef[];

export const routes = initialRoute.concat(authRoute, dashboardRoute);
