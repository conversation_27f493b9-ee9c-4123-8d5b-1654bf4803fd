/* eslint-disable @typescript-eslint/no-explicit-any */
import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { EmpBatchResponseDef } from "@/models/response/emp-batch";
import { useGetEmpBatchRecordQuery } from "@/services/employee-batch.service";
import { sheet } from "@/store/module/sheet";
import { skipToken } from "@reduxjs/toolkit/query";
import { ColDef } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import BaseTable from "./BaseTable";

const BatchRecordTable = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);
  const {
    data: { id },
  } = metadata as { data: EmpBatchResponseDef };

  const { data: response, isLoading } = useGetEmpBatchRecordQuery(
    id ? { id } : skipToken,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<any>[]>([
    {
      headerName: "First Name",
      field: "payload.firstName",
    },
    {
      headerName: "Last Name",
      field: "payload.lastName",
    },
    {
      headerName: "Phone Number",
      field: "payload.phone1",
    },
    {
      headerName: "Failure Reason",
      field: "failureReason",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={response?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      hasActionOnRow={false}
      actionOptions={{
        showDefault: true,
        actions: [
          {
            title: "View Records",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.batchRecordSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default BatchRecordTable;
