import ProductCard from "../cards/ProductCard";
import SectionHeader from "../headers/SectionHeader";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";

const productsData = [
  {
    image: "/images/nescafe.png",
    name: "Nescafé Clasico Coffee Blend",
    price: 1000,
    rating: 4,
  },
  {
    image: "/images/twix.png",
    name: "Hollandia Yoghurt 200ml",
    price: 1000,
    rating: 4,
  },
  {
    image: "/images/nescafe.png",
    name: "Hollandia Yoghurt 200ml",
    price: 1000,
    rating: 4,
  },
  {
    image: "/images/floss.png",
    name: "Oral B Healthy Dental floss",
    price: 1000,
    rating: 4,
  },
  {
    image: "/images/milk.png",
    name: "HLoya Milk Refill 2000g",
    price: 1000,
    rating: 4,
  },
  {
    image: "/images/bread.png",
    name: "Hollandia Bread",
    price: 1000,
    rating: 4,
  },
  {
    image: "/images/twix.png",
    name: "Hollandia Yoghurt 200ml",
    price: 1000,
    rating: 4,
  },
  {
    image: "/images/nescafe.png",
    name: "Hollandia Yoghurt 200ml",
    price: 1000,
    rating: 4,
  },
  {
    image: "/images/nescafe.png",
    name: "Hollandia Yoghurt 200ml",
    price: 1000,
    rating: 4,
  },
];

const TopSearchedProducts = () => {
  return (
    <section className="wrapper">
      <SectionHeader className="bg-[#807AF9] text-white" title="Top Searched" />
      <ScrollArea>
        <div className="flex gap-4 py-4">
          {productsData.map((product, idx) => (
            <ProductCard product={product} key={idx} />
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </section>
  );
};

export default TopSearchedProducts;
