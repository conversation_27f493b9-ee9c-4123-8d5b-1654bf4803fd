// services/authApi.ts

import { CompleteEnrollmentDef } from "@/models/request/enrollment/complete-enrollment";
import { InitiateEnrollmentRequestDef } from "@/models/request/enrollment/initiate-enrollment.request";
import { DefaultResponse } from "@/models/response/default.response";
import { InitiateEnrollmentResponeDef } from "@/models/response/initiate-enrollment.response";
import { baseService } from "./base.service";

export const enrollmentService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    initiateEnrollment: builder.mutation<
      InitiateEnrollmentResponeDef,
      InitiateEnrollmentRequestDef
    >({
      query: (credentials) => ({
        url: "/enrollment/initiate",
        method: "POST",
        body: credentials,
      }),
    }),
    resendOtp: builder.mutation<DefaultResponse, { otpRef: string }>({
      query: (credentials) => ({
        url: "/enrollment/resend-otp",
        method: "POST",
        body: credentials,
      }),
    }),
    completeEnrollment: builder.mutation<
      DefaultResponse & { data: { slug: string } },
      CompleteEnrollmentDef
    >({
      query: (credentials) => ({
        url: "/enrollment/complete",
        method: "POST",
        body: credentials,
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  useInitiateEnrollmentMutation,
  useCompleteEnrollmentMutation,
  useResendOtpMutation,
} = enrollmentService;
