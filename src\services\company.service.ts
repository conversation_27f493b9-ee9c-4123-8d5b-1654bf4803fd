// services/authApi.ts

import { CreateCompanyRequestDef } from "@/models/request/company/create-company.request";
import { CompanyResponseDef } from "@/models/response/company/company.response";
import { DefaultResponse } from "@/models/response/default.response";
import { baseService } from "./base.service";

export const companyService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    getCompanyData: builder.query<
      DefaultResponse & { data: CompanyResponseDef },
      { slug: string }
    >({
      query: (credentials) => ({
        url: `/company/${credentials.slug}`,
        method: "GET",
      }),
      providesTags: ["Company"],
    }),
    createCompany: builder.mutation<
      DefaultResponse & { data: CompanyResponseDef },
      CreateCompanyRequestDef
    >({
      query: (payload) => ({
        url: "/company/create",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Company"],
    }),
  }),
  overrideExisting: false,
});

export const { useGetCompanyDataQuery, useCreateCompanyMutation } =
  companyService;
