import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { AuthorizationResponseDef } from "@/models/response/authorization";
import { useGetAuthorizationRequestQuery } from "@/services/authorization.service";
import { sheet } from "@/store/module/sheet";
import { ColDef } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import BaseTable from "./BaseTable";

const AuthorizationQueueTable = () => {
  const { data: authorizationRequests, isLoading } =
    useGetAuthorizationRequestQuery(undefined, {
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true,
    });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<AuthorizationResponseDef>[]>([
    {
      headerName: "Action",
      field: "action",
    },
    {
      headerName: "Entity",
      field: "module",
    },
    {
      headerName: "Request",
      field: "data",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={authorizationRequests?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        // defaultEditAction: onEditClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.authorizationReviewSheet,
                  metadata: {
                    data: node.data,
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default AuthorizationQueueTable;
