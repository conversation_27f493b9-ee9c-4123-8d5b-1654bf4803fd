import { Button } from "@/components/ui/button";
import { CustomCellRendererProps } from "ag-grid-react";
import { Trash2Icon} from "lucide-react";
import { useCallback } from "react";

interface IamUserActionCellRendererProps extends CustomCellRendererProps {
    onItemRemoved?: (removedItem: any) => void; // Callback to notify parent
}

const IamUserActionCellRenderer = ({ node, onItemRemoved }: IamUserActionCellRendererProps) => {

  const onRemoveClick = useCallback(async () => {
    const rowData = node.data;

    if (onItemRemoved) {
          const data = {
              confirmDelete: true,
              rowData: rowData
          }
          onItemRemoved({data})
    }
  }, [onItemRemoved]);

  // const navigate = useNavigate();

  // const onManageClick = () => {
  //   dispatch(role.mutation.setCurrentRoleView(node.data.roleName));
  //   navigate(
  //     `${RouteConstant.dashboard.accessManagement.roles.index}/${node.data.roleId}`
  //   );
  // };



    return (
        <>
            <div className="flex items-center space-x-2 mt-0.5">

                <Button
                    variant="ghost"
                    className="hover:bg-red-100"
                    onClick={onRemoveClick}
                >
                    <Trash2Icon className="text-destructive"/>
                </Button>
            </div>

        </>

    );
};

export default IamUserActionCellRenderer;
