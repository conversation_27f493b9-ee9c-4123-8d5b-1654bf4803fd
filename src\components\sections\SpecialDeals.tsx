import PromoCard from "../cards/PromoCard";
import SectionHeader from "../headers/SectionHeader";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";

const promoData = [
  {
    image: "/images/grocery.png",
    name: "Grocery deals",
  },
  {
    image: "/images/appliance.png",
    name: "Appliance Deals",
  },
  {
    image: "/images/clothing.png",
    name: "Clothing deals",
  },
  {
    image: "/images/sales.png",
    name: "Sales deals",
  },
  {
    image: "/images/appliance.png",
    name: "Appliance Deals",
  },
  {
    image: "/images/sales.png",
    name: "Sales deals",
  },
];

const SpecialDeals = () => {
  return (
    <section className="wrapper">
      <SectionHeader title="Special Deals" />
      <ScrollArea className="w-full border flex items-center justify-center  px-4 py-2 rounded-md">
        <div className="flex gap-4 py-4">
          {promoData.map((promo, idx) => (
            <PromoCard promo={promo} key={idx} />
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </section>
  );
};

export default SpecialDeals;
