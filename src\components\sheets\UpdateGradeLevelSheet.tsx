"use client";

import { useAppSelector } from "@/hooks";
import { GradeResponseDef } from "@/models/response/grade";
import UpdateGradeLevelForm from "../forms/update/UpdateGradeLevelForm";
import { DialogDescription } from "../ui/dialog";
import { SheetHeader, SheetTitle } from "../ui/sheet";

const UpdateGradeLevelSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: GradeResponseDef };
  return (
    <>
      <SheetHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <SheetTitle className="">Update grade level</SheetTitle>
        <DialogDescription className="max-w-sm">{`Update "${data.name}" branch`}</DialogDescription>
      </SheetHeader>
      <UpdateGradeLevelForm />
    </>
  );
};

export default UpdateGradeLevelSheet;
