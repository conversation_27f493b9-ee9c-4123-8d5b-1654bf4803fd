"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateGradeLevelForm from "../forms/CreateGradeLevelForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const CreateEmployeeGroupModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new grade level</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateGradeLevelForm />
    </>
  );
};

export default CreateEmployeeGroupModal;
