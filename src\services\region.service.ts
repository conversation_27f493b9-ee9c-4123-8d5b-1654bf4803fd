// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { UnitResponseDef } from "@/models/response/unit";
import { NameDescriptionDef } from "@/models/validations";
import { baseService } from "./base.service";

export const regionService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createRegion: builder.mutation<DefaultResponse, NameDescriptionDef>({
      query: (credentials) => ({
        url: "/region",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getRegions: builder.query<
      DefaultResponse & { data: UnitResponseDef[] },
      void
    >({
      query: () => ({
        url: "/region",
        method: "GET",
      }),
      providesTags: ["Regions"],
    }),
  }),
  overrideExisting: false,
});

export const { useCreateRegionMutation, useGetRegionsQuery } = regionService;
