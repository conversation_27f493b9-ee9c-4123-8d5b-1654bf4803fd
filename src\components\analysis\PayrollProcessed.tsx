"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, XAxis } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { DashboardResponseDef } from "@/models/response/dashboard";

const chartConfig = {
  basicSalary: {
    label: "Basic Salary",
    color: "#34d399",
  },
  deductions: {
    label: "Deductions",
    color: "hsl(var(--chart-2))",
  },
  allowances: {
    label: "Allowances",
    color: "#fbbf24",
  },
  netPay: {
    label: "Net Pay",
    color: "#60a5fa",
  },
} satisfies ChartConfig;

const PayrollProcessed = ({
  breakdownByMonth,
}: {
  breakdownByMonth: DashboardResponseDef["monthlyBreakdown"];
}) => {
  const monthlyBreakdown = Object.entries(breakdownByMonth)
    .sort(([monthA], [monthB]) => monthA.localeCompare(monthB)) // ascending
    .map(([month, values]) => ({
      month,
      ...values,
    }));

  console.log("Monthly Breakdown Data:", monthlyBreakdown);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payroll Expenses Breakdown</CardTitle>
        <CardDescription>Tooltip with line indicator.</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <BarChart accessibilityLayer data={monthlyBreakdown} barSize={30}>
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => {
                const [year, month] = value.split("-");
                const date = new Date(Number(year), Number(month) - 1);
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  year: "numeric",
                });
              }}
            />
            <Bar
              dataKey="basicSalary"
              stackId="a"
              fill="#34d399" // solid green
              radius={[0, 0, 4, 4]}
            />
            <Bar
              dataKey="netPay"
              stackId="a"
              fill="#60a5fa" // solid blue
              radius={[0, 0, 0, 0]}
            />
            <Bar
              dataKey="allowances"
              stackId="a"
              fill="#fbbf24" // solid yellow
              radius={[0, 0, 0, 0]}
            />
            <Bar
              dataKey="deductions"
              stackId="a"
              fill="#f87171" // solid red
              radius={[4, 4, 0, 0]}
            />
            <ChartTooltip
              content={<ChartTooltipContent indicator="line" />}
              cursor={false}
              defaultIndex={1}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

export default PayrollProcessed;
