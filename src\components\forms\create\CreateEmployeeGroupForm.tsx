import { <PERSON>mbo<PERSON><PERSON><PERSON><PERSON>, Input<PERSON>ield } from "@/components/form-fields";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { currencies } from "@/data/currencies";
import {
  CreateEmployeeGroupDef,
  CreateEmployeeGroupSchema,
} from "@/models/validations/employee-group/create-employee-group.validation";
// import { useCreateEmployeeGroupMutation } from "@/services/employee-group.service";
import { useGetGradeQuery } from "@/services/grade.service";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const CreateEmployeeGroupForm = () => {
  // const dispatch = useAppDispatch();

  const { data: response, isLoading } = useGetGradeQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  // const [createEmployeeGroup, { isLoading: isCreatingEmployee, error }] =
  //   useCreateEmployeeGroupMutation();

  const form = useForm<CreateEmployeeGroupDef>({
    resolver: zodResolver(CreateEmployeeGroupSchema),
    defaultValues: {},
    mode: "all",
  });

  const grossSalary = form.watch("grossSalary");
  const tax = form.watch("tax");
  const otherDeduction = form.watch("otherDeductions");

  useEffect(() => {
    const gross = grossSalary ? Number(grossSalary.replaceAll(",", "")) : 0;
    const other = otherDeduction
      ? Number(otherDeduction.replaceAll(",", ""))
      : 0;
    const taxPercent = tax ? Number(tax.replaceAll(",", "")) : 0;

    const taxAmount = (taxPercent / 100) * gross;
    const net = gross - taxAmount - other;

    form.setValue("netSalary", net.toLocaleString());
  }, [grossSalary, tax, otherDeduction, form]);

  const onSubmit = async (data: CreateEmployeeGroupDef) => {
    // try {
    //   const res = await createEmployeeGroup({
    //     ...data,
    //     grossSalary: Number(data.grossSalary.replaceAll(",", "")),
    //     otherDeductions: data.otherDeductions
    //       ? Number(data.otherDeductions.replaceAll(",", ""))
    //       : 0,
    //     tax: Number(data.tax.replaceAll(",", "")),
    //   }).unwrap();

    //   if (res.success) {
    //     toast("Employee group request submitted and is pending authorization");
    //     dispatch(modal.mutation.close());
    //   }
    // } catch {
    //   if (error && typeof error === "object" && "data" in error) {
    //     const errData = error.data as { message?: string };
    //     toast(errData.message ?? "Something went wrong.");
    //   } else {
    //     toast("Something went wrong. Try again!");
    //   }

    //   // toast(error)
    // }
    console.log(data);
  };

  console.log(form.formState.errors);

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <>
              <ComboBoxField
                options={
                  response
                    ? response?.data.map((grade) => ({
                        label: grade.name,
                        value: grade.id,
                      }))
                    : []
                }
                labelKey="label"
                valueKey="value"
                form={form}
                name="jobGradeId"
                label="Select grade"
                isLoading={isLoading}
              />
              <InputField form={form} name="name" label="Grade level name" />
              <p className="font-semibold text-primary text-sm">
                SALARY DETAILS
              </p>
              <div className="flex flex-col items-start gap-4">
                <ComboBoxField
                  options={currencies}
                  labelKey="code"
                  valueKey="code"
                  form={form}
                  name="currency"
                  label="Currency Code"
                />
                <InputField
                  form={form}
                  name="grossSalary"
                  label="Gross Salary"
                  formatAsCurrency
                  description="Total salary before deductions"
                />
              </div>
              <div className="flex items-start gap-x-4">
                <InputField
                  form={form}
                  name="tax"
                  label="Tax %"
                  formatAsCurrency
                  description="Total salary before deductions"
                />
                <InputField
                  form={form}
                  name="otherDeductions"
                  label="Other deductions"
                  formatAsCurrency
                />
              </div>

              <InputField
                form={form}
                name="netSalary"
                label="Net Salary"
                readOnly
                disabled
              />

              <div className="flex gap-4 justify-between">
                <Button className="font-medium" disabled={false}>
                  {/* {isCreatingEmployee && (
                    <LoaderIcon className="animate-spin" />
                  )} */}
                  Create new grade level
                </Button>
              </div>
            </>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default CreateEmployeeGroupForm;
