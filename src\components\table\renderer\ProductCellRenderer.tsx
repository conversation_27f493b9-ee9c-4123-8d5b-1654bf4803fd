import { CustomCellRendererProps } from "ag-grid-react";

export const ProductCellRenderer = ({
  node,
}: CustomCellRendererProps<{
  image: string;
  name: string;
  sku: string;
  category: string;
  status: string;
  stock: number;
  amount: number;
}>) => {
  return (
    <div className="flex items-center gap-2 h-full">
      <img src="/images/orange.png" alt="Product" className="w-10 h-10" />
      <p className="text-sm ">{node.data?.name}</p>
    </div>
  );
};
