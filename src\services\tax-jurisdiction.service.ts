// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { UnitResponseDef } from "@/models/response/unit";
import { NameDescriptionDef } from "@/models/validations";
import { baseService } from "./base.service";

export const taxJurisdictionService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createTaxJurisdiction: builder.mutation<
      DefaultResponse,
      NameDescriptionDef
    >({
      query: (credentials) => ({
        url: "/tax-jurisdiction/create",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    uploadTaxJurisdiction: builder.mutation<
      DefaultResponse,
      { items: NameDescriptionDef[] }
    >({
      query: (payload) => ({
        url: "/tax-jurisdiction/bulk/create",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getTaxJurisdictions: builder.query<
      DefaultResponse & { data: UnitResponseDef[] },
      void
    >({
      query: () => ({
        url: "/tax-jurisdiction",
        method: "GET",
      }),
      providesTags: ["TaxJurisdictions"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateTaxJurisdictionMutation,
  useGetTaxJurisdictionsQuery,
  useUploadTaxJurisdictionMutation,
} = taxJurisdictionService;
