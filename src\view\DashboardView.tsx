import PayrollProcessed from "@/components/analysis/PayrollProcessed";
import TaxAndDeductions from "@/components/analysis/TaxAndDeductions";
import TotalEmployee from "@/components/analysis/TotalEmployee";
import { useGetAnalysisQuery } from "@/services/dashboard.service";

const DashboardView = () => {
  const { data: response } = useGetAnalysisQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <TotalEmployee employeeCount={response?.data.totalEmployeee || 0} />
        <TaxAndDeductions
          totalDeductions={response?.data.predictedTotalDeductions || 0}
        />
      </div>
      {response?.data && (
        <PayrollProcessed breakdownByMonth={response?.data.monthlyBreakdown} />
      )}
    </div>
  );
};

export default DashboardView;
