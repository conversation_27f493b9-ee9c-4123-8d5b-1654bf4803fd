import { z } from "zod";

export const CreatePayrollRecordSchema = z.object({
  staffCode: z.string().min(1, "Required"),

  firstName: z.string().min(1, "Required"),
  lastName: z.string().min(1, "Required"),
  middleName: z.string().optional(),

  tax: z.string(),
  pension: z.string(),
  basicSalary: z.string().min(1, "Required"),
  allowances: z.string(),
  deductions: z.string(),
  netPay: z.string().min(1, "Required"),
});

export type CreatePayrollRecordDef = z.infer<typeof CreatePayrollRecordSchema>;
