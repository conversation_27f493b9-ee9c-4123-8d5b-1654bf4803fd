import { useAppDispatch } from "@/hooks";
import {
  CreateGradeLevelDef,
  CreateGradeLevelSchema,
} from "@/models/validations/grade-level/create-grade-level.validation";
import { useCreateGradeLevelMutation } from "@/services/employee-group.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { InputField, TextareaField } from "../form-fields";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateGradeLevelForm = () => {
  const dispatch = useAppDispatch();

  const [createGradeLevel, { isLoading: isCreatingEmployee, error }] =
    useCreateGradeLevelMutation();

  const form = useForm<CreateGradeLevelDef>({
    resolver: zodResolver(CreateGradeLevelSchema),
    defaultValues: {},
    mode: "all",
  });

  const onSubmit = async (data: CreateGradeLevelDef) => {
    try {
      const res = await createGradeLevel({
        ...data,
        description: data.description || "",
      }).unwrap();

      if (res.success) {
        toast("Grade level request submitted and is pending authorization");
        dispatch(modal.mutation.close());
      }
    } catch {
      console.log(error);
    }
  };

  console.log(form.formState.errors);

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <>
              <InputField form={form} name="name" label="Grade level name" />

              <TextareaField
                form={form}
                name="description"
                label="Description"
              />

              <div className="flex gap-4 justify-between">
                <Button className="font-medium" disabled={isCreatingEmployee}>
                  {isCreatingEmployee && (
                    <LoaderIcon className="animate-spin" />
                  )}
                  Create new grade level
                </Button>
              </div>
            </>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default CreateGradeLevelForm;
