"use client";

import { useAppSelector } from "@/hooks";
import { GradeResponseDef } from "@/models/response/grade";
import UpdateGradeForm from "../forms/update/UpdateGradeForm";
import { DialogDescription } from "../ui/dialog";
import { SheetHeader, SheetTitle } from "../ui/sheet";

const UpdateGradeSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: GradeResponseDef };
  return (
    <>
      <SheetHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <SheetTitle className="">{`Update cadre`}</SheetTitle>
        <DialogDescription className="max-w-sm">{`Update "${data.name}"`}</DialogDescription>
      </SheetHeader>
      <UpdateGradeForm />
    </>
  );
};

export default UpdateGradeSheet;
