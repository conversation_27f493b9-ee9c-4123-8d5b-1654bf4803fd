import { useAppSelector } from "@/hooks";
import { LoginResponseDef } from "@/models/response/auth/login.response";
import { DefaultResponse } from "@/models/response/default.response";
import {
  RootLoginDef,
  RootLoginSchema,
} from "@/models/validations/auth/login.validation";
import { useLoginMutation } from "@/services/auth.service";
import { auth } from "@/store/module/auth";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { InputField } from "../form-fields";
import { Button } from "../ui/button";
import { DialogDescription, DialogHeader, DialogTitle } from "../ui/dialog";
import { Form } from "../ui/form";

const SessionExpiredModal = () => {
  const dispatch = useDispatch();
  const { data: accountData } = useAppSelector((state) => state.account);
  const { data: selectedCompany } = useAppSelector((state) => state.company);
  const { account } = useAppSelector((state) => state.auth);

  console.log(account);

  const cid = localStorage.getItem("cid");

  const [login, { isLoading }] = useLoginMutation();

  const form = useForm<RootLoginDef>({
    resolver: zodResolver(RootLoginSchema),
    defaultValues: {
      email: account?.userEmail,
      password: "",
    },
    mode: "all",
  });

  // Reset email and password when the user type changes
  const onSubmit = async (data: RootLoginDef) => {
    let res: DefaultResponse & { data: LoginResponseDef };
    if (account.isRoot) {
      res = await login(data).unwrap();
    } else {
      res = await login({
        ...data,
        accountId:
          String(account.userId) === String(accountData?.id)
            ? undefined
            : accountData?.id,
        companyId: selectedCompany?.id || cid!,
      }).unwrap();
    }

    console.log(res);

    dispatch(auth.mutation.setToken(res.data.access_token));
    // dispatch(
    //   auth.mutation.setAccount({
    //     id: companyId,
    //     userEmail: email,
    //     userId: id,
    //     userRoleId: roleId,
    //   })
    // );
    dispatch(modal.mutation.close());
  };

  return (
    <>
      <DialogHeader className="!bg-white  z-10 flex pt-6">
        <DialogTitle className="">Session Expired</DialogTitle>
        <DialogDescription className="max-w-sm">
          Please re-enter your password to continue.
        </DialogDescription>
      </DialogHeader>
      <div className="bg-white  w-full max-w-sm">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
            <InputField
              form={form}
              name="password"
              label="Password"
              type="password"
              placeholder="••••••••"
            />
            <Button className="w-full font-medium" disabled={isLoading}>
              {isLoading && <LoaderIcon className="animate-spin" />}
              Sign in
            </Button>
          </form>
        </Form>
      </div>
    </>
  );
};

export default SessionExpiredModal;
