import {
  MdOutlineStar,
  MdOutlineStarBorder,
  MdOutlineStarHalf,
} from "react-icons/md";
interface StarRatingProps {
  rating: number;
}
const Rating = ({ rating }: StarRatingProps) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className="flex">
      {/* Full Stars */}
      {[...Array(fullStars)].map((_, index) => (
        <MdOutlineStar key={index} className="fill-[#FFC500]" size={16} />
      ))}
      {/* Half Star */}
      {hasHalfStar && (
        <MdOutlineStarHalf className="fill-[#FFC500]" size={16} />
      )}
      {/* Empty Stars */}
      {[...Array(emptyStars)].map((_, index) => (
        <MdOutlineStarBorder
          key={index + fullStars}
          className="fill-[#FFC500]"
          size={16}
        />
      ))}
    </div>
  );
};

export default Rating;
