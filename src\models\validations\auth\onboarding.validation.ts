import { z } from "zod";

export const OnboardingSchema = z.object({
  account: z.object({
    email: z.string().email("Invalid company email"),
    name: z.string().min(1, "Account name is required"),
  }),
  company: z.object({
    email: z.string().email("Invalid company email"),
    phoneNumber: z.string().min(1, "Phone number is required"), // use string instead of number for better UX in forms
    name: z.string().min(1, "Company name is required"),
    logo: z.string().url("Invalid logo URL").optional(),
    address: z.string().min(1, "Company address is required"),
    state: z.string().min(1, "State is required"),
    city: z.string().min(1, "City is required"),
    zipCode: z.string().optional(),
    website: z.string().optional(),
    description: z.string().optional(),
    registrationNumber: z.string().optional(),
    industry: z.string().min(1, "Industry is required"),
    industryType: z.string().min(1, "Industry type is required"),
    country: z.string().min(1, "Country is required"),
  }),
  user: z
    .object({
      password: z
        .string()
        .trim()
        .min(6, "Password must be at least 6 characters"),
      confirmPassword: z.string().trim(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    }),
});

export type OnboardingDef = z.infer<typeof OnboardingSchema>;
