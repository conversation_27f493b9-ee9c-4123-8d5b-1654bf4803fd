import { Wallet } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "../ui/card";

const TaxAndDeductions = ({ totalDeductions }: { totalDeductions: number }) => {
  return (
    <Card className="bg-[#fcecec] w-80 h-40 flex flex-col justify-between">
      <CardHeader>
        <div className="flex gap-4">
          <div className="bg-[#990a00] w-10 h-10 rounded-full flex items-center justify-center">
            <Wallet className="text-white w-4 h-" strokeWidth={2} />
          </div>
          <div>
            <p className="font-light">Tax & Deductions</p>
            <p className="font-bold text-xl">{totalDeductions}</p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="text-sm">
        90% of employees are full-time staff
      </CardContent>
    </Card>
  );
};

export default TaxAndDeductions;
