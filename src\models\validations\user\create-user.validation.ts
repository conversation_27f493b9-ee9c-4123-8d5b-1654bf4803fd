import { z } from "zod";

export const CreateUserSchema = z.object({
  name: z.string().min(1, {message: "Enter name"}),
  email: z.string().email({
    message: "Invalid email address",
  }),
  password: z.string().min(1, {message: "Enter password"}),
  // companyId: z.string(),
  branches: z.array(z.string()).optional(),
  // locations: z.string().optional(),
  roleId: z.number().int(),
  twoFactorEnabled: z.boolean().optional(),
  hasAccessToAllBranches: z.boolean().optional(),
  // regionalUser: z.boolean().optional(),
});

export type CreateUserDef = z.infer<typeof CreateUserSchema>;
