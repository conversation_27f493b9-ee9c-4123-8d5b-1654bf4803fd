export interface AddressInfo {
  residenceState: string;
  residenceLocalGovt: string;
  stateOfOrigin: string;
  localGovt: string;
  placeOfBirth: string;
  streetAddress: string;
}

export interface NextOfKin {
  nextOfKinFullName: string;
  nextOfKinRelationship: string;
  nextOfKinPhoneNumber: string;
  nextOfKinEmail: string;
  nextOfKinAddress: string;
}

export interface EducationInfo {
  highestQualification: string;
  institutionName: string;
  institutionAddress?: string;
  dateOfGraduation: string;
}

export interface AccountInfo {
  accountNumber?: string;
  accountName?: string;
  accountStatus?: string;
}

export interface GuarantorInfo {
  guarantorPassport?: string;
  guarantorFullname: string;
  guarantorPhoneNumber: string;
  guarantorRelationShip: string;
  guarantorAddress: string;
  guarantorOccupation: string;
  guarantorMeansOfIdentification?: string;
}

export interface SalaryInfo {
  grossSalary: string;
  tax: string;
  otherDeductions?: string;
  currency: string;
}

export interface CreateEmployeeRequestDef {
  // Basic Info
  title: string;
  gradeLevelName?: string;
  unitName?: string;
  contractTypeName?: string;
  staffCode?: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  birthday: string;

  // Personal Info
  gender: string;
  maritalStatus: string;
  nationality: string;
  stateOfOrigin: string;
  localGovt?: string;
  residentialAddress: string;
  residentialLocalGovt?: string;
  residentialState: string;
  residentialCountry: string;

  // Contact
  phone1: string;
  phone2?: string;
  email: string;
  placeOfBirth?: string;
  religion?: string;

  // Next of Kin
  nextOfKinFullName?: string;
  nextOfKinRelationship?: string;
  nextOfKinPhoneNumber?: string;
  nextOfKinEmail?: string;
  nextOfKinAddress?: string;

  // Education
  highestQualification?: string;
  course?: string;
  institutionName?: string;
  dateOfGraduation?: string;
  dateEmployed: string;

  // Identity
  bvn: string;
  nin: string;
  kycVerified?: boolean;

  // Spouse & Children
  nameOfSpouse?: string;
  noOfChildren?: string;
  accountNumber?: string;
  taxId?: string;
  pensionId?: string;
  pfa?: string;
  dateAppointedToLevel?: string;

  // Guarantor
  passport?: string;
  certificate?: string;
  guarantorPassport?: string;
  guarantorFullname?: string;
  guarantorPhoneNumber?: string;
  guarantorRelationShip?: string;
  guarantorAddress?: string;
  guarantorOccupation?: string;
  guarantorMeansOfIdentification?: string;

  // Work
  branchName: string;
  jobTitleName?: string;
  jobGradeName?: string;
  departmentName?: string;
  salaryPackageName: string;
}
