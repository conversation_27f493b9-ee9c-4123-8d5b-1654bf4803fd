/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppSelector } from "@/hooks";
import { TriangleAlertIcon } from "lucide-react";
import { Button } from "../ui/button";
import { DialogClose, DialogHeader, DialogTitle } from "../ui/dialog";

const DeleteConfirmatonModal = ({
  handleDelete,
}: {
  handleDelete: () => Promise<any>;
}) => {
  // const dispatch = useAppDispatch();
  const {
    modalOptions: { metadata },
    isLoading,
  } = useAppSelector((state) => state.modal);

  const { description, title, warning } = metadata as {
    id: number;
    entity: string;
    title: string;
    description: string;
    warning: string;
  };

  const onDeleteClick = async () => {
    await handleDelete();
  };

  return (
    <div className="min-w-80 py-4">
      <DialogHeader>
        <DialogTitle className="font-medium text-center text-base">
          {title}
        </DialogTitle>
      </DialogHeader>
      <div className="mt-4">
        <p className="text-center text-sm text-neutral-500">{description}</p>

        <div className=" border-[#F4C8CF] bg-[#F9E1E5] text-[#AF233A] p-2 rounded-md mt-4">
          <p className="flex items-center text-base font-medium">
            <TriangleAlertIcon className="w-4 h-4 mr-1" />
            Warning
          </p>
          <p className="mt-3 text-sm">{warning}</p>
        </div>
      </div>
      <div className="mt-5 flex items-center justify-center gap-4">
        <DialogClose>
          <Button variant="outline" size="sm">
            Cancel
          </Button>
        </DialogClose>
        <Button
          onClick={onDeleteClick}
          variant="destructive"
          size="sm"
          disabled={isLoading}
        >
          Delete
        </Button>
      </div>
    </div>
  );
};

export default DeleteConfirmatonModal;
