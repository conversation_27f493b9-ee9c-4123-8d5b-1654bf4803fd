import { z } from "zod";

const OtherAllowanceSchema = z.object({
  id: z.string().min(1, "Required"),
});

const OtherDeductionSchema = z.object({
  id: z.string().min(1, "Required"),
});

const SalaryPackageSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1, "Required"),
  description: z.string().optional(),

  baseSalary: z.string().min(1, "Required"),

  // Statutory Deductions
  pensionRate: z.string().min(1, "Required"),
  nhfRate: z.string().min(1, "Required"),
  pension: z.boolean(),
  nhf: z.boolean(),
  taxAmount: z.string().min(1, "Required"),

  // Currency
  currency: z.string().default("NGN"),

  // New Allowance Fields
  apprenticeAllowance: z.string().optional(),
  housingAllowance: z.string().optional(),
  transportAllowance: z.string().optional(),
  utilityAllowance: z.string().optional(),
  selfMaintenanceAllowance: z.string().optional(),
  hazardOrEntertainmentAllowance: z.string().optional(),
  furnitureAllowance: z.string().optional(),
  fuelSubsidy: z.string().optional(),
  domesticStaffAllowance: z.string().optional(),
  childEducationSubsidy: z.string().optional(),
  levelProficiencyAllowance: z.string().optional(),
  responsibilityAllowance: z.string().optional(),

  monthlyGrossSalary: z.string().optional(),
  annualGrossSalary: z.string().optional(),

  // Relations
  allowances: z.array(OtherAllowanceSchema).default([]),
  deductions: z.array(OtherDeductionSchema).default([]),
});

export { OtherAllowanceSchema, OtherDeductionSchema, SalaryPackageSchema };
export type CreateSalaryPackageDef = z.infer<typeof SalaryPackageSchema>;
