export function calculatePAYETax2025(
  grossIncome: number,
  options?: { nhf?: boolean; pension?: boolean }
): {
  nhf: number;
  pension: number;
  tax: number;
} {
  const rentRelief = 200_000;

  const { nhf: useNhf = false, pension: usePension = false } = options || {};

  const nhf = useNhf ? grossIncome * 0.025 : 0;
  const pension = usePension ? grossIncome * 0.08 : 0;

  const totalReliefs = rentRelief + nhf + pension;
  const taxableIncome = Math.max(grossIncome - totalReliefs, 0);

  const brackets = [
    { upTo: 800_000, rate: 0 },
    { upTo: 3_000_000, rate: 0.15 },
    { upTo: 12_000_000, rate: 0.18 },
    { upTo: 25_000_000, rate: 0.21 },
    { upTo: 50_000_000, rate: 0.23 },
    { upTo: Infinity, rate: 0.25 },
  ];

  let tax = 0;
  let prevLimit = 0;

  for (const { upTo, rate } of brackets) {
    if (taxableIncome <= prevLimit) break;

    const taxableAtThisRate = Math.min(taxableIncome, upTo) - prevLimit;
    tax += taxableAtThisRate * rate;

    prevLimit = upTo;
  }

  return {
    nhf: Math.round(nhf),
    pension: Math.round(pension),
    tax: Math.round(tax),
  };
}
