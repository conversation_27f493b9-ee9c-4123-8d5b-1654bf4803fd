import { SheetConstant } from "@/constants/SheetConstant";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export type SheetState = {
  isLoading: boolean;
  data?: { [key: string]: unknown };
  sheetOptions: {
    open?: boolean;
    component?: SheetConstant;
    disableCloseOnBlur?: boolean;
    metadata?: { [key: string]: unknown };
  };
};
const initialState: SheetState = {
  isLoading: false,
  sheetOptions: {
    open: false,
    disableCloseOnBlur: false,
    metadata: {},
    component: undefined,
  },
};

const actions = {};

const slice = createSlice({
  name: "sheet",
  initialState,
  reducers: {
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    open: (state, action: PayloadAction<SheetState["sheetOptions"]>) => {
      state.sheetOptions = {
        ...state.sheetOptions,
        ...action.payload,
        open: true,
      };
    },
    setData: (state, action: PayloadAction<SheetState["data"]>) => {
      state.data = {
        ...action.payload,
      };
    },
    close: (state) => ({
      ...state,
      sheetOptions: initialState.sheetOptions,
    }),
  },
});

export const sheet = {
  reducer: slice.reducer,
  actions: actions,
  mutation: slice.actions,
};
