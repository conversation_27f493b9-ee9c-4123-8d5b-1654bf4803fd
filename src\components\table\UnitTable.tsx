import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { UnitResponseDef } from "@/models/response/unit";
import { useGetUnitsQuery } from "@/services/unit.service";
import { sheet } from "@/store/module/sheet";
import { ColDef, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import BaseTable from "./BaseTable";

const UnitTable = () => {
  const { data: response, isLoading } = useGetUnitsQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<UnitResponseDef>[]>([
    {
      headerName: "Name",
      field: "name",
    },
    {
      headerName: "Description",
      field: "description",
    },
    {
      headerName: "Tax Jurisdiction",
      field: "taxJurisdiction",
    },
    {
      headerName: "Region",
      field: "region",
      valueGetter: ({ node }) => node?.data?.region || "-",
    },
    {
      headerName: "Employee Count",
      field: "_count.employee",
    },
    {
      headerName: "Created by",
      field: "createdBy",
    },
  ]);

  const onEditClick = (node: IRowNode<UnitResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.entityViewSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={response?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default UnitTable;
