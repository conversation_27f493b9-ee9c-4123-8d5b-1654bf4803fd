// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { UserResponseDef } from "@/models/response/users";
import { CreateUserDef } from "@/models/validations/user/create-user.validation";
import { UpdateUserDef } from "@/models/validations/user/update-user.validation";
import { baseService } from "./base.service";

export const userService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createUser: builder.mutation<DefaultResponse, CreateUserDef>({
      query: (credentials) => ({
        url: "/users",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    updateUser: builder.mutation<DefaultResponse, UpdateUserDef>({
      query: (credentials) => ({
        url: "/users/update",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    deleteUser: builder.mutation<DefaultResponse, { id: string }>({
      query: (payload) => ({
        url: "/users/delete",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getUsers: builder.query<
      DefaultResponse & { data: UserResponseDef[] },
      void
    >({
      query: () => ({
        url: "/users",
        method: "GET",
      }),
      providesTags: ["Users"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateUserMutation,
  useGetUsersQuery,
  useUpdateUserMutation,
  useDeleteUserMutation,
} = userService;
