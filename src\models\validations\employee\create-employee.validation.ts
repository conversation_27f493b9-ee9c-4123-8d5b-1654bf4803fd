import { z } from "zod";

export const CreateEmployeeSchema = z.object({
  title: z.string(),
  staffCode: z.string().optional(),

  firstName: z.string(),
  lastName: z.string(),
  middleName: z.string().optional(),
  birthday: z.date(),

  gender: z.string(),
  maritalStatus: z.string(),
  nationality: z.string(),

  stateOfOrigin: z.string(),
  localGovt: z.string(),

  residentialAddress: z.string(),
  residentialLocalGovt: z.string(),
  residentialState: z.string(),
  residentialCountry: z.string(),

  phone1: z.string(),
  phone2: z.string().optional(),

  email: z.string().email(),

  placeOfBirth: z.string(),
  religion: z.string(),

  nextOfKinFullName: z.string(),
  nextOfKinRelationship: z.string(),
  nextOfKinPhoneNumber: z.string(),
  nextOfKinEmail: z
    .string()
    .optional()
    .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: "Invalid email address",
    }),
  nextOfKinAddress: z.string(),

  highestQualification: z.string().optional(),
  course: z.string().optional().optional(),
  institutionName: z.string().optional(),
  dateOfGraduation: z.date().optional(),

  dateEmployed: z.date(),

  bvn: z.string(),
  nin: z.string(),

  nameOfSpouse: z.string().optional(),
  noOfChildren: z.string().optional(),

  taxId: z.string().optional(),
  pensionId: z.string().optional(),
  pfa: z.string().optional(),

  dateAppointedToLevel: z.date(),

  accountNumber: z.string().optional(),
  passport: z.string().optional(),
  certificate: z.string().optional(),
  guarantorPassport: z.string().optional(),
  guarantorFullname: z.string().optional(),
  guarantorPhoneNumber: z.string().optional(),
  guarantorRelationShip: z.string().optional(),
  guarantorAddress: z.string().optional(),
  guarantorOccupation: z.string().optional(),
  guarantorMeansOfIdentification: z.string().optional(),

  branchName: z.string(),
  subBranchName: z.string().optional(),
  jobTitleName: z.string(),
  gradeLevelName: z.string().optional(),
  salaryPackageName: z.string(),
  departmentName: z.string().optional(),
  jobGradeName: z.string().optional(),
  unitName: z.string().optional(),
  contractTypeName: z.string().optional(),
});

export type CreateEmployeeDef = z.infer<typeof CreateEmployeeSchema>;
