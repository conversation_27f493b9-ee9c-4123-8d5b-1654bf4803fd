import { MenuConstant } from "@/constants/MenuConstants";
import { cn } from "@/lib/utils";
import { MenuIcon } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "../ui/button";

const MobileMenu = () => {
  const [showMenu, setShowMenu] = useState(false);

  return (
    <>
      <Button className="lg:hidden" onClick={() => setShowMenu(!showMenu)}>
        <MenuIcon />
      </Button>

      <div
        className={cn(
          "absolute h-screen bg-red-500  transition-transform duration-500 -translate-x-full opacity-0",
          showMenu && "translate-x-0 opacity-100"
        )}
      >
        <nav className="flex flex-col w-screen max-w-80 gap-4">
          {MenuConstant.map((menuItem) => (
            <a href={menuItem.path}>{menuItem.label}</a>
          ))}
        </nav>
      </div>
    </>
  );
};

export default MobileMenu;
