"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateSubBranchForm from "../forms/CreateSubBranchForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const SubBranchModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new sub-branch</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateSubBranchForm />
    </>
  );
};

export default SubBranchModal;
