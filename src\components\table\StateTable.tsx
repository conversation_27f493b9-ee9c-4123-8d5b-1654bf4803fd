import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { UnitResponseDef } from "@/models/response/unit";
import { useGetTaxJurisdictionsQuery } from "@/services/tax-jurisdiction.service";
import { sheet } from "@/store/module/sheet";
import { ColDef, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import BaseTable from "./BaseTable";

const StateTable = () => {
  const { data: response, isLoading } = useGetTaxJurisdictionsQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<UnitResponseDef>[]>([
    {
      headerName: "Name",
      field: "name",
    },
    {
      headerName: "Description",
      field: "description",
    },
    {
      headerName: "Created by",
      field: "createdBy",
    },
  ]);

  const onEditClick = (node: IRowNode<UnitResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.entityViewSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={response?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default StateTable;
