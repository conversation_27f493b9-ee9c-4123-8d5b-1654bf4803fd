"use client";

import BatchRecordTable from "../table/BatchRecordTable";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";
import { SheetHeader, SheetTitle } from "../ui/sheet";

const BatchRecordSheet = () => {
  return (
    <>
      <SheetHeader>
        <div className="flex items-center gap-2">
          <SheetTitle className="font-medium text-left !text-base">
            Batch Records Details
          </SheetTitle>
        </div>
      </SheetHeader>
      <ScrollArea className="h-[calc(100%-5rem)] border-t p-5">
        <BatchRecordTable />
        <ScrollBar />
      </ScrollArea>
    </>
  );
};

export default BatchRecordSheet;
