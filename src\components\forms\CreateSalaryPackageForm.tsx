import { Button } from '@/components/ui/button';
import { currencies } from '@/data/currencies';
import { useAppDispatch } from '@/hooks';
import { calculatePAYETax2025 } from '@/lib/calculatePAYETax';
import { CreateSalaryPackageRequestDef } from '@/models/request/salary-package/create-salary-package.request';
import { CreateSalaryPackageDef, SalaryPackageSchema } from '@/models/validations/salary-package/create-salary-package.validation';
import { useCreateSalaryPackageMutation } from '@/services/salary-package.service';
import { modal } from '@/store/module/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { LoaderIcon } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { CheckBoxField, ComboBoxField, InputField, TextareaField } from '../form-fields';
import PayeBreakdown from '../PayeBreakDown';
import { Form } from '../ui/form';

const allowanceFields = [
  'apprenticeAllowance',
  'housingAllowance',
  'transportAllowance',
  'utilityAllowance',
  'selfMaintenanceAllowance',
  'hazardOrEntertainmentAllowance',
  'furnitureAllowance',
  'fuelSubsidy',
  'domesticStaffAllowance',
  'childEducationSubsidy',
  'levelProficiencyAllowance',
  'responsibilityAllowance',
] as const;

const CreateSalaryPackageForm = () => {
  const dispatch = useAppDispatch();

  const [createSalaryPackage, { isLoading: isCreatingSalaryPackage }] = useCreateSalaryPackageMutation();

  const form = useForm({
    resolver: zodResolver(SalaryPackageSchema),
    defaultValues: {
      name: '',
      baseSalary: '0',
      pensionRate: '0',
      nhfRate: '0',
      pension: false,
      nhf: false,
      taxAmount: '0',
      currency: 'NGN',

      apprenticeAllowance: '0',
      housingAllowance: '0',
      transportAllowance: '0',
      utilityAllowance: '0',
      selfMaintenanceAllowance: '0',
      hazardOrEntertainmentAllowance: '0',
      furnitureAllowance: '0',
      fuelSubsidy: '0',
      domesticStaffAllowance: '0',
      childEducationSubsidy: '0',
      levelProficiencyAllowance: '0',
      responsibilityAllowance: '0',
      monthlyGrossSalary: '0',
      annualGrossSalary: '0',

      allowances: [],
      deductions: [],
    },
    mode: 'all',
  });

  const onSubmit = async (payload: CreateSalaryPackageDef) => {
    const data: CreateSalaryPackageRequestDef = {
      name: payload.name,
      currency: payload.currency,
      description: payload.description,
      nhfRate: Number(payload.nhfRate.replaceAll(",", "")) || 0,
      pensionRate: Number(payload.pensionRate.replaceAll(",", "")) || 0,
      allowances: [],
      deductions: [],
      taxAmount: Number(payload.taxAmount.replaceAll(',', '')) || 0,
      baseSalary: Number(payload.baseSalary.replaceAll(',', '')) || 0,
      monthlyGrossSalary: Number(payload?.monthlyGrossSalary?.replaceAll(',', '')) || 0,
      annualGrossSalary: Number(payload?.annualGrossSalary?.replaceAll(',', '')) || 0,
      apprenticeAllowance: Number(payload?.apprenticeAllowance?.replaceAll(',', '')) || 0,
      housingAllowance: Number(payload?.housingAllowance?.replaceAll(',', '')) || 0,
      transportAllowance: Number(payload?.transportAllowance?.replaceAll(',', '')) || 0,
      utilityAllowance: Number(payload?.utilityAllowance?.replaceAll(',', '')) || 0,
      selfMaintenanceAllowance: Number(payload?.selfMaintenanceAllowance?.replaceAll(',', '')) || 0,
      hazardOrEntertainmentAllowance: Number(payload?.hazardOrEntertainmentAllowance?.replaceAll(',', '')) || 0,
      furnitureAllowance: Number(payload?.furnitureAllowance?.replaceAll(',', '')) || 0,
      fuelSubsidy: Number(payload?.fuelSubsidy?.replaceAll(',', '')) || 0,
      domesticStaffAllowance: Number(payload?.domesticStaffAllowance?.replaceAll(',', '')) || 0,
      childEducationSubsidy: Number(payload?.childEducationSubsidy?.replaceAll(',', '')) || 0,
      levelProficiencyAllowance: Number(payload?.levelProficiencyAllowance?.replaceAll(',', '') || 0),
      responsibilityAllowance: Number(payload?.responsibilityAllowance?.replaceAll(',', '') || 0),
    };

    try {
      const res = await createSalaryPackage(data).unwrap();
      if (res.success) {
        toast.success(res.message);
        dispatch(modal.mutation.close());
      }
    } catch (error) {
      console.log(error);
    }
  };

  const rawGrossIncome = form.watch('annualGrossSalary');
  const nhf = form.watch('nhf');
  const pension = form.watch('pension');
  const baseSalary = form.watch('baseSalary');

  // Replace the problematic useWatch with individual watch calls
  const allowancesValues = allowanceFields.map(field => form.watch(field as keyof CreateSalaryPackageDef));

  const rawMonthlySalary = form.watch('monthlyGrossSalary');

  useEffect(() => {
    const grossIncome = Number(rawGrossIncome?.replaceAll(',', ''));

    if (!isNaN(grossIncome) && grossIncome > 0) {
      const deduction = calculatePAYETax2025(grossIncome, {
        nhf,
        pension,
      });

      console.log(deduction);

      form.setValue(
        'pensionRate',
        (deduction.pension / 12).toLocaleString(undefined, {
          maximumFractionDigits: 2,
        })
      );
      form.setValue(
        'nhfRate',
        (deduction.nhf / 12).toLocaleString(undefined, {
          maximumFractionDigits: 2,
        })
      );
      form.setValue(
        'taxAmount',
        (deduction.tax / 12).toLocaleString(undefined, {
          maximumFractionDigits: 2,
        })
      );
    }
  }, [rawGrossIncome, nhf, pension, form]);

  useEffect(() => {
    const base = Number(form.watch('baseSalary')?.replaceAll(',', '') || 0);

    const totalAllowances = allowanceFields.reduce((acc, key) => {
      const formattedValue = form.getValues(key as keyof CreateSalaryPackageDef) as string;
      const value = Number(formattedValue.replaceAll(',', '') || 0);
      return acc + value;
    }, 0);

    const gross = base + totalAllowances;

    if (!isNaN(gross)) {
      form.setValue(
        'monthlyGrossSalary',
        gross.toLocaleString(undefined, {
          maximumFractionDigits: 2,
        })
      );
    }
  }, [form, baseSalary, allowancesValues]);

  useEffect(() => {
    const monthlySalary = Number(rawMonthlySalary?.replaceAll(',', ''));

    if (!isNaN(monthlySalary) && monthlySalary > 0) {
      form.setValue(
        'annualGrossSalary',
        (monthlySalary * 12).toLocaleString(undefined, {
          maximumFractionDigits: 2,
        })
      );
    }
  }, [rawMonthlySalary, form]);

  return (
    <section className="min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 items-start">
              <InputField form={form} name="name" label="Name" />
              <ComboBoxField options={currencies} labelKey="code" valueKey="code" form={form} name="currency" label="Currency Code" />
              <CheckBoxField form={form} name="nhf" label="NHF" />

              <CheckBoxField form={form} name="pension" label="Pension" />

              <InputField form={form} name="baseSalary" label="Base Salary" formatAsCurrency />

              <InputField form={form} name="monthlyGrossSalary" label="Monthly Gross Salary" formatAsCurrency />

              <InputField form={form} name="annualGrossSalary" label="Annual Gross Salary" formatAsCurrency />

              <InputField form={form} name="pensionRate" label="Pension Rate" formatAsNumber disabled />
              <InputField form={form} name="nhfRate" label="NHF Rate" formatAsNumber disabled />
              <InputField form={form} name="taxAmount" label="Tax Amount" formatAsCurrency disabled />
              <InputField form={form} name="apprenticeAllowance" label="Apprentice Allowance" formatAsCurrency />
              <InputField form={form} name="housingAllowance" label="Housing Allowance" formatAsCurrency />
              <InputField form={form} name="transportAllowance" label="Transport Allowance" formatAsCurrency />
              <InputField form={form} name="utilityAllowance" label="Utility Allowance" formatAsCurrency />
              <InputField form={form} name="selfMaintenanceAllowance" label="Self Maintenance Allowance" formatAsCurrency />
              <InputField form={form} name="hazardOrEntertainmentAllowance" label="Hazard/Entertainment Allowance" formatAsCurrency />
              <InputField form={form} name="furnitureAllowance" label="Furniture Allowance" formatAsCurrency />
              <InputField form={form} name="fuelSubsidy" label="Fuel Subsidy" formatAsCurrency />
              <InputField form={form} name="domesticStaffAllowance" label="Domestic Staff Allowance" formatAsCurrency />
              <InputField form={form} name="childEducationSubsidy" label="Child Education Subsidy" formatAsCurrency />
              <InputField form={form} name="levelProficiencyAllowance" label="Level Proficiency Allowance" formatAsCurrency />
              <InputField form={form} name="responsibilityAllowance" label="Responsibility Allowance" formatAsCurrency />
            </div>
            <TextareaField form={form} name="description" label="Description" placeholder="Enter package description" />
            <PayeBreakdown grossIncome={Number(rawGrossIncome?.replaceAll(',', ''))} nhf={nhf} pension={pension} />
            <div className="flex gap-4 justify-end">
              <Button className="font-medium" disabled={isCreatingSalaryPackage}>
                {isCreatingSalaryPackage && <LoaderIcon className="animate-spin" />}
                Submit
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default CreateSalaryPackageForm;
