import { useAppSelector } from "@/hooks";

const AuthFormHeader = ({
  description,
  title,
  showImg = true,
}: {
  title: string;
  description?: string;
  showImg?: boolean;
}) => {
  const { data } = useAppSelector((state) => state.company);
  return (
    <section className="mb-5">
      {showImg && (
        <img
          src={data?.logo || "https://www.corebanknigeria.com/corebank.svg"}
          className=" h-10 object-cover mx-auto text-[0px]"
          alt="MarketSquare Logo"
        />
      )}
      <h1 className="font-semibold text-3xl text-center mt-6">{title}</h1>
      {description && (
        <p className="text-center text-sm text-[#475467] mt-3">{description}</p>
      )}
    </section>
  );
};

export default AuthFormHeader;
