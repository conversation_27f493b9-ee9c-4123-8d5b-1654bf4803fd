import { Input<PERSON>ield, TextareaField } from "@/components/form-fields";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { DepartmentResponseDef } from "@/models/response/department";
import {
  UpdateDepartmentDef,
  UpdateDepartmentSchema,
} from "@/models/validations/department/update-department.validation";
import { useUpdateDepartmentMutation } from "@/services/department.service";
import { sheet } from "@/store/module/sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const UpdateDepartmentForm = () => {
  const dispatch = useAppDispatch();

  const [updateDepartment, { isLoading: isCreatingRole }] =
    useUpdateDepartmentMutation();

  const {
    sheetOptions: { metadata: sheetMetadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = sheetMetadata as { data: DepartmentResponseDef };

  const form = useForm<UpdateDepartmentDef>({
    resolver: zodResolver(UpdateDepartmentSchema),
    defaultValues: {
      id: data.id,
      description: data.description,
      name: data.name,
    },
    mode: "all",
  });

  const onSubmit = async (data: UpdateDepartmentDef) => {
    const res = await updateDepartment(data).unwrap();

    if (res.success) {
      toast("Department request submitted and is pending authorization");
      dispatch(sheet.mutation.close());
    }
  };

  return (
    <section className="p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="name"
              label="Name"
              placeholder="Enter department name"
            />

            <TextareaField
              form={form}
              name="description"
              label="Description "
              placeholder="Enter department description"
            />
          </div>
          <Button className="w-full font-medium" disabled={isCreatingRole}>
            {isCreatingRole && <LoaderIcon className="animate-spin" />}
            Update Department
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default UpdateDepartmentForm;
