import { calculatePAYETax2025 } from "@/lib/calculatePAYETax";
import { formattedCurrency } from "@/lib/utils";
import { useMemo } from "react";

type Props = {
  grossIncome: number;
  nhf?: boolean;
  pension?: boolean;
};

const PayeBreakdown = ({
  grossIncome,
  nhf = false,
  pension = false,
}: Props) => {
  const rentRelief = 200_000;

  const deduction = useMemo(
    () => calculatePAYETax2025(grossIncome, { nhf, pension }),
    [grossIncome, nhf, pension]
  );

  const totalRelief = rentRelief + deduction.nhf + deduction.pension;
  const taxableIncome = Math.max(grossIncome - totalRelief, 0);

  const monthlyTax = deduction.tax / 12;
  const effectiveRate = ((deduction.tax / grossIncome) * 100).toFixed(1);

  const breakdown = [
    { label: "First ₦800,000 @ 0%", limit: 800_000, rate: 0 },
    { label: "Next ₦2,200,000 @ 15%", limit: 3_000_000, rate: 0.15 },
    { label: "Next ₦9,000,000 @ 18%", limit: 12_000_000, rate: 0.18 },
    { label: "Next ₦13,000,000 @ 21%", limit: 25_000_000, rate: 0.21 },
    { label: "Next ₦25,000,000 @ 23%", limit: 50_000_000, rate: 0.23 },
    { label: "Above ₦50,000,000 @ 25%", limit: Infinity, rate: 0.25 },
  ];

  let remaining = taxableIncome;
  let prev = 0;

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 space-y-8 w-full max-w-3xl mx-auto">
      <div className="space-y-1">
        <h2 className="text-2xl font-bold">PAYE Calculation</h2>
        <p className="text-gray-500 text-sm">
          Based on Nigeria’s updated income tax law
        </p>
      </div>

      <div className="grid sm:grid-cols-2 gap-4 text-sm">
        <div className="font-medium">Gross Annual Income:</div>
        <div>{formattedCurrency(grossIncome)}</div>

        <div className="font-medium">Rent Relief:</div>
        <div>-{formattedCurrency(rentRelief)}</div>

        {nhf && (
          <>
            <div className="font-medium">NHF Deduction (2.5%):</div>
            <div>-{formattedCurrency(deduction.nhf)}</div>
          </>
        )}

        {pension && (
          <>
            <div className="font-medium">Pension Deduction (8%):</div>
            <div>-{formattedCurrency(deduction.pension)}</div>
          </>
        )}

        <div className="font-medium">Total Deductions:</div>
        <div>-{formattedCurrency(totalRelief)}</div>

        <div className="font-medium">Taxable Income:</div>
        <div>{formattedCurrency(taxableIncome)}</div>
      </div>

      <div>
        <h3 className="font-semibold text-base mb-3">Tax Bracket Breakdown</h3>
        <div className="overflow-auto rounded border border-gray-200">
          <table className="w-full text-sm min-w-[600px]">
            <thead className="bg-gray-100 text-left">
              <tr>
                <th className="p-2">Bracket</th>
                <th className="p-2">Taxable Amount</th>
                <th className="p-2">Tax Due</th>
              </tr>
            </thead>
            <tbody>
              {breakdown.map(({ label, limit, rate }) => {
                if (remaining <= 0) return null;
                const amount = Math.min(limit - prev, remaining);
                const tax = amount * rate;
                remaining -= amount;
                prev = limit;

                return (
                  <tr key={label} className="border-t border-gray-100">
                    <td className="p-2">{label}</td>
                    <td className="p-2">{formattedCurrency(amount)}</td>
                    <td className="p-2">{formattedCurrency(tax)}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      <div className="border-t pt-4 text-sm grid sm:grid-cols-2 gap-4">
        <div className="font-medium">Monthly Salary (After Tax):</div>
        <div>{formattedCurrency(grossIncome / 12 - monthlyTax)}</div>

        <div className="font-medium">Monthly PAYE Tax:</div>
        <div>{formattedCurrency(monthlyTax)}</div>

        <div className="font-medium">Effective Tax Rate:</div>
        <div>{effectiveRate}%</div>
      </div>
    </div>
  );
};

export default PayeBreakdown;
