import { createSlice, PayloadAction } from "@reduxjs/toolkit";

type CompanyState = {
  data: {
    id: string;
    name: string;
    status: string;
    logo: string;
    registrationNumber: string;
  } | null;
  isLoading: boolean;
  error: string | null;
};

const initialState: CompanyState = {
  data: null,
  isLoading: false,
  error: null,
};

const slice = createSlice({
  name: "company",
  initialState,
  reducers: {
    setCompany: (
      state,
      action: PayloadAction<{
        id: string;
        name: string;
        status: string;
        logo: string;
        registrationNumber: string;
      }>
    ) => {
      state.data = action.payload;
      state.error = null;
    },
    clearCompany: (state) => {
      state.data = null;
      state.error = null;
    },
    setCompanyLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setCompanyError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const company = {
  reducer: slice.reducer,
  mutation: slice.actions,
};
