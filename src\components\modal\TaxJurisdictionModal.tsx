"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateNewTaxJurisdictionForm from "../forms/CreateNewTaxJurisdictionForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const TaxJurisdictionModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new tax jurisdiction</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateNewTaxJurisdictionForm />
    </>
  );
};

export default TaxJurisdictionModal;
