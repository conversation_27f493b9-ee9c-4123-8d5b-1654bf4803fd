"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateRoleForm from "../forms/CreateRoleForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const CreateRoleModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new role</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateRoleForm />
    </>
  );
};

export default CreateRoleModal;
