import { baseService } from "@/services/base.service";
import { configureStore } from "@reduxjs/toolkit";
import localforage from "localforage";
import {
  FLUSH,
  PAUSE,
  PERSIST,
  persistReducer,
  persistStore,
  PURGE,
  REGISTER,
  REHYDRATE,
} from "redux-persist";
import autoMergeLevel2 from "redux-persist/es/stateReconciler/autoMergeLevel2";
import rootReducer, { RootReducerState } from "./module";

// LocalForage configuration
localforage.config({
  driver: [localforage.INDEXEDDB, localforage.WEBSQL, localforage.LOCALSTORAGE],
  size: 1000,
  name: "organa",
  version: 1.0,
  storeName: "organa-store",
  description: "Storage for Finlake application",
});

localforage.ready().catch((err) => {
  console.warn("Switching to fallback storage:", err);
  localforage.setDriver(localforage.LOCALSTORAGE);
});

// Persist configuration
const persistConfig = {
  key: "root",
  timeout: 10000,
  storage: localforage,
  version: 1.0,
  stateReconciler: autoMergeLevel2,
  blacklist: ["table", "sheet", "onboarding", "modal"], // Blacklist example
};

// Create Persisted Reducer
const persistedReducer = persistReducer<RootReducerState>(
  persistConfig,
  rootReducer
);

// Configure Store with serialization handling
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore redux-persist actions
        ignoredActions: [
          FLUSH,
          REHYDRATE,
          PAUSE,
          PERSIST,
          PURGE,
          REGISTER,
          "base/setModalOptions",
        ],
        // Ignore paths that might contain functions/complex objects
        ignoredPaths: [],
      },
    }).concat(baseService.middleware),
});

export const persistor = persistStore(store);

// Ensure application doesn't render until rehydration is complete
export const waitForRehydration = () => {
  return new Promise((resolve) => {
    const unsubscribe = store.subscribe(() => {
      const state = store.getState();
      if (state._persist?.rehydrated) {
        unsubscribe();
        resolve(true);
      }
    });
  });
};

// Infer the type of makeStore
export type AppStore = typeof store;
// Infer the RootState and AppDispatch types from the store itself
export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];
