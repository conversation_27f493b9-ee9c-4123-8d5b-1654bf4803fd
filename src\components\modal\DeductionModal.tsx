"use client";

import { useAppSelector } from "@/hooks/index.ts";
import { GradeLevelResponseDef } from "@/models/response/grade-level/index.ts";
import { DialogTitle } from "@radix-ui/react-dialog";
import CreateDeductionForm from "../forms/CreateDeductionForm.tsx";
import { DialogDescription, DialogHeader } from "../ui/dialog.tsx";

const DeductionModal = () => {
  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);

  const {
    data: { name },
  } = metadata as { data: GradeLevelResponseDef };

  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new deduction</DialogTitle>
        <DialogDescription className="max-w-sm">
          {`Add new deduction for "${name}" grade level`}
        </DialogDescription>
      </DialogHeader>
      <CreateDeductionForm />
    </>
  );
};

export default DeductionModal;
