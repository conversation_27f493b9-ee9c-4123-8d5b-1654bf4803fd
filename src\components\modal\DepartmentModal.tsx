"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateDepartmentForm from "../forms/CreateDepartmentForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const DepartmentModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new department</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateDepartmentForm />
    </>
  );
};

export default DepartmentModal;
