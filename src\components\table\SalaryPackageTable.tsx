import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { EmployeeResponseDef } from "@/models/response/employee";
import { SalaryPackageResponseDef } from "@/models/response/salary-package";
import { useGetSalaryPackagesQuery } from "@/services/salary-package.service";
import { sheet } from "@/store/module/sheet";
import { ColDef, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import BaseTable from "./BaseTable";

const SalaryPackageTable = () => {
  const { data: branches, isLoading } = useGetSalaryPackagesQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<SalaryPackageResponseDef>[]>([
    {
      headerName: "Name",
      field: "name",
    },
    {
      headerName: "Currency",
      field: "currency",
    },
    {
      headerName: "Basic Salary",
      field: "baseSalary",
    },
    {
      headerName: "Monthly Gross Salary",
      field: "monthlyGrossSalary",
    },
    {
      headerName: "Annual Gross Salary",
      field: "annualGrossSalary",
    },
    {
      headerName: "PAYE",
      field: "taxAmount",
    },
    {
      headerName: "Pension",
      field: "pensionRate",
    },
    {
      headerName: "NHF",
      field: "nhfRate",
    },

    {
      headerName: "Housing Allowance",
      field: "housingAllowance",
    },
    {
      headerName: "Transport Allowance",
      field: "transportAllowance",
    },
    {
      headerName: "Utility Allowance",
      field: "utilityAllowance",
    },
    {
      headerName: "Responsibility Allowance",
      field: "responsibilityAllowance",
    },

    {
      headerName: "Apprentice Allowance",
      field: "apprenticeAllowance",
    },
    {
      headerName: "Self-Maintenance Allowance",
      field: "selfMaintenanceAllowance",
    },
    {
      headerName: "Hazard/Entertainment Allowance",
      field: "hazardOrEntertainmentAllowance",
    },
    {
      headerName: "Furniture Allowance",
      field: "furnitureAllowance",
    },
    {
      headerName: "Fuel Subsidy",
      field: "fuelSubsidy",
    },
    {
      headerName: "Domestic Staff Allowance",
      field: "domesticStaffAllowance",
    },
    {
      headerName: "Child Education Subsidy",
      field: "childEducationSubsidy",
    },
    {
      headerName: "Level Proficiency Allowance",
      field: "levelProficiencyAllowance",
    },

    // Metadata (last)
    {
      headerName: "Created By",
      field: "createdBy",
    },
    {
      headerName: "Approved By",
      field: "approvedBy",
    },
  ]);

  const onEditClick = (node: IRowNode<EmployeeResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.entityViewSheet,
        metadata: {
          title: "Update account information",
          data: node.data,
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={branches?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default SalaryPackageTable;
