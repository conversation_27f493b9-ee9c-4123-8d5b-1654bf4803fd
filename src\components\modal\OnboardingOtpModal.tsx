"use client";

import { ModalConstant } from "@/constants/ModalConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import {
  CompleteOnboardingDef,
  CompleteOnboardingSchema,
} from "@/models/validations/onboarding/complete-onboarding.validation";
import {
  useCompleteEnrollmentMutation,
  useResendOtpMutation,
} from "@/services/enrollment.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { DialogTitle } from "@radix-ui/react-dialog";
import { LoaderIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { OtpInputField } from "../form-fields";
import { Button } from "../ui/button";
import { DialogDescription, DialogHeader } from "../ui/dialog";
import { Form } from "../ui/form";

const COOLDOWN_SECONDS = 60;

const OnboardingOtpModal = () => {
  const dispatch = useAppDispatch();
  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);

  const { uniqueRef, email, otpRef } = metadata as {
    uniqueRef: string;
    email: string;
    otpRef: string;
  };

  const [completeEnrollment, { isLoading }] = useCompleteEnrollmentMutation();
  const [resendOtp, { isLoading: isResending }] = useResendOtpMutation();
  const [cooldown, setCooldown] = useState(0);

  const form = useForm<CompleteOnboardingDef>({
    resolver: zodResolver(CompleteOnboardingSchema),
    defaultValues: {
      uniqueRef,
      otp: "",
    },
    mode: "all",
  });

  const onSubmit = async (data: CompleteOnboardingDef) => {
    const res = await completeEnrollment(data).unwrap();
    if (res.success) {
      dispatch(
        modal.mutation.open({
          disableCloseOnBlur: true,
          modalType: ModalConstant.accountCreatedSuccessModal,
          metadata: {
            slug: res.data.slug,
          },
        })
      );
    }
  };

  const handleResendOtp = async () => {
    const res = await resendOtp({ otpRef }).unwrap();
    if (res.success) {
      setCooldown(COOLDOWN_SECONDS);
      toast.success("OTP has been resent to your email.");
    }
  };

  // Cooldown timer effect
  useEffect(() => {
    if (cooldown <= 0) return;
    const timer = setInterval(() => {
      setCooldown((prev) => prev - 1);
    }, 1000);
    return () => clearInterval(timer);
  }, [cooldown]);

  return (
    <div className="max-w-sm w-full">
      <DialogHeader>
        <DialogTitle className="font-bold mt-2 text-xl sm:text-3xl max-w-sm">
          Verify your email
        </DialogTitle>
        <DialogDescription>
          {`We have sent a verification code to your email address "${email}". Please enter the code below to verify your email and complete the onboarding process.`}
        </DialogDescription>
      </DialogHeader>

      <section className="my-4">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 bg-white rounded-md"
          >
            <OtpInputField
              form={form}
              name="otp"
              maxLength={6}
              label="Enter OTP"
              className="min-[400px]:w-14 min-[400px]:h-14 w-11 h-11"
            />

            <Button className="w-full font-medium" disabled={isLoading}>
              {isLoading && <LoaderIcon className="animate-spin" />}
              Verify OTP
            </Button>
          </form>
        </Form>

        <div className="mt-4 text-center">
          <p className="text-sm">Didn't get the code?</p>

          <Button
            variant="link"
            className="text-blue-600 text-sm"
            onClick={handleResendOtp}
            disabled={isResending || cooldown > 0}
          >
            {isResending
              ? "Resending..."
              : cooldown > 0
              ? `Resend OTP in ${cooldown}s`
              : "Resend OTP"}
          </Button>
        </div>
      </section>
    </div>
  );
};

export default OnboardingOtpModal;
