import { Sheet, SheetContent } from "@/components/ui/sheet";
import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { sheet } from "@/store/module/sheet";
import {
  BatchRecordSheet,
  CreateCompanySheet,
  EntityViewSheet,
  JsonSheet,
  PayrollBatchSheet,
  PayrollRecordSheet,
  UpdateBranchSheet,
  UpdateContractTypeSheet,
  UpdateDepartmentSheet,
  UpdateGradeLevelSheet,
  UpdateGradeSheet,
  UpdateRoleSheet,
  UpdateUserSheet,
} from "../sheets";
import AuthorizationSheet from "../sheets/AuthorizationSheet";
import UpdateDesignationSheet from "../sheets/UpdateDesignationSheet";
const SheetProvider = () => {
  const dispatch = useAppDispatch();

  const {
    sheetOptions: { open, component, disableCloseOnBlur },
  } = useAppSelector((state) => state.sheet);

  const renderSheetContent = () => {
    switch (component) {
      case SheetConstant.jsonSheet:
        return <JsonSheet />;
      case SheetConstant.entityViewSheet:
        return <EntityViewSheet />;
      case SheetConstant.authorizationReviewSheet:
        return <AuthorizationSheet />;
      case SheetConstant.batchRecordSheet:
        return <BatchRecordSheet />;
      case SheetConstant.updateRoleSheet:
        return <UpdateRoleSheet />;
      case SheetConstant.payrollBatchSheet:
        return <PayrollBatchSheet />;
      case SheetConstant.updateUserSheet:
        return <UpdateUserSheet />;
      case SheetConstant.updateBranchSheet:
        return <UpdateBranchSheet />;
      case SheetConstant.updateGradeSheet:
        return <UpdateGradeSheet />;
      case SheetConstant.updateGradeLevelSheet:
        return <UpdateGradeLevelSheet />;
      case SheetConstant.updateDesignationSheet:
        return <UpdateDesignationSheet />;
      case SheetConstant.updateContractTypeSheet:
        return <UpdateContractTypeSheet />;
      case SheetConstant.updateDepartmentSheet:
        return <UpdateDepartmentSheet />;
      case SheetConstant.createCompanySheet:
        return <CreateCompanySheet />;
      case SheetConstant.payrollRecordSheet:
        return <PayrollRecordSheet />;

      default:
        return null;
    }
  };
  return (
    <Sheet
      open={open}
      onOpenChange={(isOpen) => {
        if (!disableCloseOnBlur && !isOpen) {
          dispatch(sheet.mutation.close());
        }
      }}
    >
      <SheetContent className="w-[400px] sm:max-w-2xl xl:max-w-4xl sm:w-full">
        {renderSheetContent()}
      </SheetContent>
    </Sheet>
  );
};

export default SheetProvider;
