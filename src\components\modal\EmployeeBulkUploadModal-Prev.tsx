// /* eslint-disable @typescript-eslint/no-explicit-any */
// import { useAppDispatch } from "@/hooks";
// import { CreateEmployeeRequestDef } from "@/models/request/employee/create-employee.request";
// import {
//   EmployeeCsvDef,
//   EmployeeCsvSchema,
// } from "@/models/validations/employee/create-bulk-employee.validation";
// import { useCreateBulkEmployeeMutation } from "@/services/employee.service";
// import { modal } from "@/store/module/modal";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { LoaderIcon } from "lucide-react";
// import Papa from "papaparse";
// import { useState } from "react";
// import { useForm } from "react-hook-form";
// import { toast } from "sonner";
// import { Button } from "../ui/button";
// import { DialogDescription, DialogHeader, DialogTitle } from "../ui/dialog";
// import { Input } from "../ui/input";

// const EmployeeBulkUploadModal = () => {
//   const dispatch = useAppDispatch();

//   const [createBulkEmployee, { isLoading }] = useCreateBulkEmployeeMutation();

//   const [mappedData, setMappedData] = useState<CreateEmployeeRequestDef[]>([]);
//   const form = useForm<EmployeeCsvDef>({
//     resolver: zodResolver(EmployeeCsvSchema),
//   });
//   const [columns, setColumns] = useState<string[]>([]);
//   const [errors, setErrors] = useState<string[]>([]);
//   const [employeeData, setEmployeeData] = useState<any[]>([]);

//   const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
//     const file = e.target.files?.[0];
//     if (!file) return;

//     Papa.parse(file, {
//       header: true,
//       skipEmptyLines: true,
//       complete: (result) => {
//         const rawRows = result.data as any[];

//         // ✅ Filter out completely empty rows
//         const rows = rawRows.filter((row) =>
//           Object.values(row).some((val) => String(val).trim() !== "")
//         );

//         setColumns(Object.keys(rows[0]));

//         setEmployeeData(rows);
//       },
//     });
//   };

//   const onSubmit = async (payload: EmployeeCsvDef) => {
//     setErrors([]);
//     const {
//       jobTitleName,
//       birthday,
//       bvn,
//       dateAppointedToLevel,
//       dateEmployed,
//       dateOfGraduation,
//       email,
//       firstName,
//       gender,
//       guarantorAddress,
//       guarantorFullname,
//       guarantorOccupation,
//       guarantorPhoneNumber,
//       guarantorRelationShip,
//       highestQualification,
//       institutionName,
//       lastName,
//       localGovt,
//       maritalStatus,
//       nationality,
//       nextOfKinAddress,
//       nextOfKinEmail,
//       nextOfKinFullName,
//       nextOfKinPhoneNumber,
//       nextOfKinRelationship,
//       nin,
//       phone1,
//       placeOfBirth,
//       religion,
//       residentialAddress,
//       residentialCountry,
//       residentialLocalGovt,
//       residentialState,
//       staffCode,
//       stateOfOrigin,
//       course,
//       middleName,
//       nameOfSpouse,
//       noOfChildren,
//       pensionId,
//       pfa,
//       phone2,
//       taxId,
//       title,
//       branchName,
//       contractTypeName,
//       departmentName,
//       gradeLevelName,
//       jobGradeName,
//       salaryPackageName,
//       unitName,
//     } = payload;

//     const mapped: CreateEmployeeRequestDef[] = [];
//     const errList: string[] = [];

//     const requiredFields = {
//       bvn,
//       nin,
//       residentialAddress,
//       residentialState,
//       residentialLocalGovt,
//       email,
//       phone1,
//       birthday,
//       salaryPackageName,
//     };

//     for (const row of employeeData) {
//       const missingRequired = Object.entries(requiredFields)
//         .filter(([_, value]) => !value)
//         .map(([key]) => key);

//       if (missingRequired.length > 0) {
//         errList.push(
//           `Missing ${
//             missingRequired.length
//               ? `required field(s): ${missingRequired.join(", ")}`
//               : ""
//           } for employee "${row[firstName]}".`
//         );
//         continue;
//       }

//       mapped.push({
//         birthday: birthday ? row[birthday] : "-",
//         branchName,
//         jobTitleName: jobTitleName ? row[jobTitleName] : null,
//         jobGradeName: jobGradeName ? row[jobGradeName] : null,
//         departmentName: departmentName ? row[departmentName] : null,
//         salaryPackageName: row[salaryPackageName],
//         unitName: unitName ? row[unitName] : null,
//         gradeLevelName: gradeLevelName ? row[gradeLevelName] : null,
//         firstName: row[firstName],
//         bvn: bvn ? row[bvn] : "-",
//         dateAppointedToLevel: dateAppointedToLevel
//           ? row[dateAppointedToLevel]
//           : "-",
//         dateEmployed: dateEmployed ? row[dateEmployed] : "-",
//         email: row[email],
//         gender: row[gender],
//         dateOfGraduation: dateOfGraduation ? row[dateOfGraduation] : "-",

//         guarantorAddress: guarantorAddress ? row[guarantorAddress] : "-",
//         guarantorFullname: guarantorFullname ? row[guarantorFullname] : "-",
//         guarantorOccupation: guarantorOccupation
//           ? row[guarantorOccupation]
//           : "-",
//         guarantorPhoneNumber: guarantorPhoneNumber
//           ? row[guarantorPhoneNumber]
//           : "-",
//         guarantorRelationShip: guarantorRelationShip
//           ? row[guarantorRelationShip]
//           : "-",
//         highestQualification: highestQualification
//           ? row[highestQualification]
//           : "-",
//         institutionName: institutionName ? row[institutionName] : "-",
//         lastName: row[lastName],
//         localGovt: localGovt ? row[localGovt] : "-",
//         maritalStatus: maritalStatus ? row[maritalStatus] : "-",
//         nationality: nationality ? row[nationality] : "-",
//         nextOfKinAddress: nextOfKinAddress ? row[nextOfKinAddress] : "-",
//         nextOfKinEmail: nextOfKinEmail ? row[nextOfKinEmail] : "-",
//         nextOfKinFullName: nextOfKinFullName ? row[nextOfKinFullName] : "-",
//         nextOfKinPhoneNumber: nextOfKinPhoneNumber
//           ? row[nextOfKinPhoneNumber]
//           : "-",
//         nextOfKinRelationship: nextOfKinRelationship
//           ? row[nextOfKinRelationship]
//           : "-",
//         nin: nin ? row[nin] : "-",
//         phone1: row[phone1].replace(/\D/g, ""),
//         placeOfBirth: placeOfBirth ? row[placeOfBirth] : "-",
//         religion: religion ? row[religion] : "-",
//         residentialAddress: row[residentialAddress],
//         residentialCountry: residentialCountry ? row[residentialCountry] : "-",
//         residentialLocalGovt: residentialLocalGovt
//           ? row[residentialLocalGovt]
//           : "-",
//         residentialState: residentialState ? row[residentialState] : "-",
//         staffCode: staffCode ? row[staffCode] : "-",
//         stateOfOrigin: stateOfOrigin ? row[stateOfOrigin] : "-",
//         course: course ? row[course] : "-",
//         middleName: middleName ? row[middleName] : "-",
//         nameOfSpouse: nameOfSpouse ? row[nameOfSpouse] : "-",
//         noOfChildren: noOfChildren ? row[noOfChildren] : "-",
//         pensionId: pensionId ? row[pensionId] : "-",
//         pfa: pfa ? row[pfa] : "-",
//         phone2: phone2 ? row[phone2] : "-",
//         taxId: taxId ? row[taxId] : "-",
//         title: title ? row[title] : "-",
//         contractTypeName: contractTypeName ? row[contractTypeName] : null,
//       });
//     }

//     console.log(mapped);

//     setMappedData(mapped);
//     setErrors(errList);

//     // return;

//     if (errList.length === 0) {
//       const res = await createBulkEmployee(mapped).unwrap();

//       if (res.success) {
//         toast(res.message);
//         dispatch(modal.mutation.close());
//       }
//     }
//   };

//   console.log(form.formState.errors);
//   console.log(form.getValues());

//   return (
//     <>
//       <DialogHeader className="sticky top-0 !bg-white z-10 flex pt-6">
//         <DialogTitle>Employee Bulk Upload</DialogTitle>
//         <DialogDescription className="max-w-sm">
//           <div>
//             <p>Upload a `.csv` file with employee information.</p>
//           </div>
//           <div className="flex gap-4 mt-2 pb-2 z-40">
//             <Input type="file" accept=".csv" onChange={handleFileUpload} />
//             <Button
//               onClick={form.handleSubmit(onSubmit)}
//               disabled={!form.formState.isValid || isLoading}
//             >
//               {isLoading && <LoaderIcon className="animate-spin" />}
//               Proceed
//             </Button>
//           </div>
//         </DialogDescription>
//       </DialogHeader>

//       <div>
//         <a
//           href="/create_employee_template.csv"
//           download="create_employee_template.csv"
//         >
//           <Button size="sm">Download Template</Button>
//         </a>
//         <p>Note: All date should be in this format "dd/MM/yyyy"</p>
//       </div>

//       {/* <Form {...form}>
//         <form onSubmit={form.handleSubmit(onSubmit)}>
//           <div className="*:grid *:grid-cols-2 *:gap-4 text-xs font-medium sm:min-w-xl lg:min-w-2xl px-2 py-4">
//             <div>
//               <p>Field</p>
//               <p>Key</p>
//             </div>
//             {EMPLOYEE_FIELD_MAPPING_CONSTANT.map((field) => (
//               <div key={field.name} className="border-b py-3">
//                 <p>{field.label}</p>
//                 <ComboBoxField
//                   className="placeholder:text-xs"
//                   placeholder="Select field"
//                   name={field.name}
//                   form={form}
//                   options={columns.map((col) => ({
//                     value: col,
//                     label: col,
//                   }))}
//                 />
//               </div>
//             ))}
//           </div>

//           {errors.length > 0 && (
//             <div className="text-red-600 text-sm mt-2">
//               <p>⚠️ Mapping errors:</p>
//               <ul className="list-disc pl-5">
//                 {errors.map((err, i) => (
//                   <li key={i}>{err}</li>
//                 ))}
//               </ul>
//             </div>
//           )}

//           <pre className="mt-4 bg-gray-100 p-2 rounded text-xs overflow-auto max-h-80">
//             {JSON.stringify(mappedData, null, 2)}
//           </pre>
//         </form>
//       </Form> */}
//     </>
//   );
// };

// export default EmployeeBulkUploadModal;
