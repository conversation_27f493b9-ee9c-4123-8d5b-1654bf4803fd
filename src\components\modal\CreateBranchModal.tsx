"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateBranchForm from "../forms/CreateBranchForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const CreateBranchModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new branch/unit</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateBranchForm />
    </>
  );
};

export default CreateBranchModal;
