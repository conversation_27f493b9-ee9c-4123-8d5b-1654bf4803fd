"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateGradeForm from "../forms/CreateGradeForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const GradeModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new cadre</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateGradeForm />
    </>
  );
};

export default GradeModal;
