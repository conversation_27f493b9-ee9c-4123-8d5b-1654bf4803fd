import { useTheme } from 'next-themes';
import { Toaster as Sonner, ToasterProps } from 'sonner';

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps['theme']}
      className="toaster group"
      style={
        {
          // Use default styling for normal toasts
          '--normal-bg': 'var(--popover)',
          '--normal-text': 'var(--popover-foreground)',
          '--normal-border': 'var(--border)',
          // Apply green styling only to success toasts
          '--success-bg': 'hsl(142.1 76.2% 36.3%)',
          '--success-text': 'hsl(355.7 100% 97.3%)',
          '--success-border': 'hsl(142.1 76.2% 36.3%)',
        } as React.CSSProperties
      }
      {...props}
    />
  );
};

export { Toaster };
