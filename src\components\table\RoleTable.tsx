import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { RoleResponseDef } from "@/models/response/role";
import { useGetRolesQuery } from "@/services/role.service";
import { sheet } from "@/store/module/sheet";
import { ColDef, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const RoleTable = () => {
  const { data, isLoading, error, requestId } = useGetRolesQuery(undefined, {
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  });

  if (error && typeof error === "object" && "data" in error) {
    const errData = error.data as { message?: string };
    toast(errData.message ?? "Something went wrong.", { id: requestId });
  }

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<RoleResponseDef>[]>([
    {
      headerName: "Name",
      field: "name",
    },
    {
      headerName: "Description",
      field: "description",
    },

    {
      headerName: "Status",
      field: "status",
    },
  ]);

  const onEditClick = (node: IRowNode<RoleResponseDef>) => {
    if (node?.data?.id === 100) {
      toast.error("Can't edit SUPER ADMIN ROLE", {
        className: "!border !border-[#F4C8CF] !bg-[#F9E1E5] !text-[#AF233A]",
      });
      return;
    }
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.updateRoleSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={data?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default RoleTable;
