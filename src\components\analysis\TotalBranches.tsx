import { Users2Icon } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "../ui/card";

const TotalBrances = () => {
  return (
    <Card className="bg-[#ECFCF4] w-80 h-40 flex flex-col justify-between">
      <CardHeader>
        <div className="flex gap-4">
          <div className="bg-[#009966] w-10 h-10 rounded-full flex items-center justify-center">
            <Users2Icon className="text-white w-4 h-" strokeWidth={2} />
          </div>
          <div>
            <p className="font-light">Total Employees</p>
            <p className="font-bold text-xl">0</p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="text-sm">
        90% of employees are full-time staff
      </CardContent>
    </Card>
  );
};

export default TotalBrances;
