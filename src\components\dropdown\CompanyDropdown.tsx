import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { useGetAccountDataQuery } from "@/services/account.service";
import { useSwitchCompanyMutation } from "@/services/auth.service";
import { account } from "@/store/module/account";
import { auth } from "@/store/module/auth";
import { company } from "@/store/module/company";
import { sheet } from "@/store/module/sheet";
import { skipToken } from "@reduxjs/toolkit/query";
import { PlusIcon } from "lucide-react";
import { Button } from "../ui/button";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "../ui/popover";
import { Avatar, AvatarImage, AvatarFallback } from "../ui/avatar";

const CompanyDropdown = () => {
  const dispatch = useAppDispatch();

  const { data: companyData } = useAppSelector((state) => state.company);
  const { data: accountData } = useAppSelector((state) => state.account);

  const { data } = useGetAccountDataQuery(
    accountData?.slug ? { slug: accountData?.slug } : skipToken,
    { refetchOnMountOrArgChange: true }
  );

  const [switchBusiness] = useSwitchCompanyMutation();

  if (data) {
    dispatch(account.mutation.setAccount(data.data));
  }

  const handleSelectCompany = async (selectedCompany: {
    id: string;
    name: string;
    status: string;
    logo: string;
    registrationNumber: string;
  }) => {
    const res = await switchBusiness({
      companyId: selectedCompany.id,
    }).unwrap();
    dispatch(auth.mutation.setToken(res.data.access_token));
    dispatch(
      auth.mutation.setAccount({
        userEmail: res.data.email,
        userId: res.data.userId,
        userRoleId: res.data.roleId,
      })
    );
    dispatch(company.mutation.setCompany(selectedCompany));
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  // Helper function to get company initials for fallback
  const getCompanyInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className="h-auto justify-start overflow-hidden w-full max-w-full p-2 sm:p-3"
          variant="outline"
        >
          <Avatar className="h-8 w-8 sm:h-10 sm:w-10 flex-shrink-0">
            <AvatarImage
              src={
                companyData?.logo ||
                "https://www.corebanknigeria.com/corebank.svg"
              }
              alt={`${companyData?.name || 'Company'} logo`}
            />
            <AvatarFallback className="text-xs sm:text-sm">
              {getCompanyInitials(companyData?.name || 'CB')}
            </AvatarFallback>
          </Avatar>
          <span className="ml-2 sm:ml-3 truncate text-sm sm:text-base min-w-0 flex-1 text-left">
            {companyData?.name}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-full min-w-[200px] max-w-[320px]">
        <div className="flex flex-col gap-2">
          {accountData?.companies.map((ac) => (
            <PopoverClose key={ac.id} className="w-full flex-1">
              <Button
                className="justify-start h-auto overflow-hidden w-full p-2 sm:p-3"
                variant="outline"
                onClick={() => handleSelectCompany(ac)}
              >
                <Avatar className="h-6 w-6 sm:h-8 sm:w-8 flex-shrink-0">
                  <AvatarImage
                    src={
                      ac?.logo || "https://www.corebanknigeria.com/corebank.svg"
                    }
                    alt={`${ac?.name || 'Company'} logo`}
                  />
                  <AvatarFallback className="text-xs">
                    {getCompanyInitials(ac?.name || 'CB')}
                  </AvatarFallback>
                </Avatar>
                <span className="ml-2 sm:ml-3 truncate text-sm min-w-0 flex-1 text-left">
                  {ac?.name}
                </span>
              </Button>
            </PopoverClose>
          ))}
          <PopoverClose className="w-full flex-1">
            <Button
              className="overflow-hidden w-full justify-start p-2 sm:p-3"
              variant="ghost"
              onClick={() =>
                dispatch(
                  sheet.mutation.open({
                    component: SheetConstant.createCompanySheet,
                  })
                )
              }
            >
              <PlusIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
              <span className="ml-2 sm:ml-3 text-sm sm:text-base">Add Company</span>
            </Button>
          </PopoverClose>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default CompanyDropdown;
