/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from "@/hooks";
import { ColDef } from "ag-grid-community";
import { Trash2Icon } from "lucide-react";
import Papa from "papaparse";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "../table/BaseTable";
import { Button } from "../ui/button";
import { DialogDescription, DialogHeader, DialogTitle } from "../ui/dialog";
import { Input } from "../ui/input";

import { useUploadTaxJurisdictionMutation } from "@/services/tax-jurisdiction.service";
import { modal } from "@/store/module/modal";

const requiredFields = ["name"];

const columnFields = ["name", "description"];

const TaxJurisdictionUploadModal = () => {
  const dispatch = useAppDispatch();

  const [createPayroll, { isLoading }] = useUploadTaxJurisdictionMutation();

  const [payrollData, setPayrollData] = useState<
    { name: string; description: string }[]
  >([]);
  const [error, setError] = useState<string | undefined>(undefined);

  const columnDefs = columnFields.map((key) => ({
    headerName: key,
    field: key,
    sortable: true,
    filter: true,
    resizable: true,
  }));
  const [colDefs] = useState<ColDef<any>[]>(columnDefs);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(undefined);
    const file = event.target.files?.[0];
    if (!file) return;

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (result) => {
        const rawRows = result.data as any[];
        const uploadedHeaders = result.meta.fields ?? [];

        const missingHeaders = requiredFields.filter(
          (field) => !uploadedHeaders.includes(field)
        );

        if (missingHeaders.length > 0) {
          setError(`Missing required columns: ${missingHeaders.join(", ")}`);
          return;
        }

        const rows = rawRows.filter((row) => {
          return columnFields.some((field) => {
            const value = row[field];
            return value !== undefined && String(value).trim() !== "";
          });
        });

        if (!rows.length) {
          toast.error("No valid data rows found in CSV");
          return;
        }

        const textFields = ["name", "description"];

        const sanitizedRows = rows.map((row) => {
          const sanitizedRow: Record<string, any> = {};

          columnFields.forEach((field) => {
            const value = row[field] ? row[field] : "-";

            if (textFields.includes(field)) {
              sanitizedRow[field] = value ?? "";
            } else {
              const numeric = parseFloat(
                String(value.trim() === "-" ? 0 : value)
                  .replace(/,/g, "")
                  .trim()
              );
              sanitizedRow[field] = isNaN(numeric) ? 0 : numeric;
            }
          });

          return sanitizedRow;
        });

        setPayrollData(
          sanitizedRows as { name: string; description: string }[]
        );
      },
    });
  };

  const handleSubmit = async () => {
    if (!payrollData.length) {
      toast.error("No data to upload");
      return;
    }

    const res = await createPayroll({
      items: payrollData,
    }).unwrap();

    if (res.success) {
      toast("Tax Jurisdiction request pending authorization!");
      dispatch(modal.mutation.close());
    }

    setPayrollData([]);
  };

  const handleDownloadTemplate = () => {
    const link = document.createElement("a");
    link.href = "/templates/tax-jurisdiction-template.csv";
    link.setAttribute("download", "tax-jurisdiction-template.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDelete = (rowIndex: number) => {
    const updatedData = [...payrollData];
    updatedData.splice(rowIndex, 1);
    setPayrollData(updatedData);
  };

  const handleClose = () => {
    setPayrollData([]);
  };

  return (
    <div className="sm:min-w-xl lg:min-w-2xl">
      <DialogHeader className="sticky top-0 !bg-white z-10 flex pt-6">
        <DialogTitle>Tax Jurisdiction Upload</DialogTitle>
        <DialogDescription className="max-w-sm">
          <p>Upload a `.csv` file with employee information.</p>
          <div className="flex flex-col gap-4 mt-2 pb-2">
            <Input type="file" accept=".csv" onChange={handleFileUpload} />
          </div>

          <Button
            onClick={handleDownloadTemplate}
            variant="link"
            className="px-0 cursor-pointer"
          >
            Download Template
          </Button>
        </DialogDescription>
      </DialogHeader>

      <div className="w-full">
        {error && (
          <div>
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}
        {payrollData.length > 0 && colDefs.length > 0 && (
          <div className="mt-4 max-h-[400px] overflow-auto border rounded">
            <BaseTable
              rowData={payrollData}
              columnDefs={colDefs}
              setPaginationPageSize={() => {}}
              showCustomPagination={false}
              actionOptions={{
                showDefault: true,
                actions: [
                  {
                    title: "Delete",
                    Icon: Trash2Icon,
                    onClick: (node) => {
                      handleDelete(node.rowIndex!);
                    },
                  },
                ],
              }}
            />
          </div>
        )}
      </div>

      {payrollData.length > 0 && colDefs.length > 0 && (
        <div className="flex justify-end gap-2 mt-4">
          <Button
            variant="outline"
            disabled={isLoading}
            size="sm"
            onClick={handleClose}
          >
            Cancel
          </Button>
          <Button disabled={isLoading} size="sm" onClick={handleSubmit}>
            Submit
          </Button>
        </div>
      )}
    </div>
  );
};

export default TaxJurisdictionUploadModal;
