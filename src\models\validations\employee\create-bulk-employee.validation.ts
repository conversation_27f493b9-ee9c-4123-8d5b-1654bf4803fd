import { z } from "zod";

export const EmployeeCsvSchema = z.object({
  title: z.string().optional(),
  gradeLevelName: z.string().min(1, "Required"),
  unitName: z.string().optional(),
  staffCode: z.string().optional(),

  firstName: z.string().min(1, "Required"),
  lastName: z.string().min(1, "Required"),
  middleName: z.string().optional(),
  birthday: z.string().optional(),

  gender: z.string().min(1, "Required"),
  maritalStatus: z.string().optional(),
  nationality: z.string().optional(),

  stateOfOrigin: z.string().optional(),
  localGovt: z.string().optional(),

  residentialAddress: z.string().min(1, "Required"),
  residentialLocalGovt: z.string().optional(),
  residentialState: z.string().optional(),
  residentialCountry: z.string().optional(),

  phone1: z.string().min(1, "Required"),
  phone2: z.string().optional(),

  email: z.string().min(1, "Required"),

  placeOfBirth: z.string().optional(),
  religion: z.string().optional(),

  nextOfKinFullName: z.string().optional(),
  nextOfKinRelationship: z.string().optional(),
  nextOfKinPhoneNumber: z.string().optional(),
  nextOfKinEmail: z.string().optional(),
  nextOfKinAddress: z.string().optional(),

  highestQualification: z.string().optional(),
  course: z.string().optional(),
  institutionName: z.string().optional(),
  dateOfGraduation: z.string().optional(),

  dateEmployed: z.string().optional(),

  bvn: z.string().optional(),
  nin: z.string().optional(),

  nameOfSpouse: z.string().optional(),
  noOfChildren: z.string().optional(),

  taxId: z.string().optional(),
  pensionId: z.string().optional(),
  pfa: z.string().optional(),

  dateAppointedToLevel: z.string().optional(),

  accountNumber: z.string().optional(),
  passport: z.string().optional(),
  certificate: z.string().optional(),
  guarantorPassport: z.string().optional(),
  guarantorFullname: z.string().optional(),
  guarantorPhoneNumber: z.string().optional(),
  guarantorRelationShip: z.string().optional(),
  guarantorAddress: z.string().optional(),
  guarantorOccupation: z.string().optional(),
  guarantorMeansOfIdentification: z.string().optional(),

  branchName: z.string().min(1, "Required"),
  jobTitleName: z.string().min(1, "Required"),
  salaryPackageName: z.string().min(1, "Required"),
  jobGradeName: z.string().min(1, "Required"),
  contractTypeName: z.string().min(1, "Required"),
  departmentName: z.string().optional(),
});

export type EmployeeCsvDef = z.infer<typeof EmployeeCsvSchema>;
