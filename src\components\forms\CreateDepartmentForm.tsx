import { useAppDispatch } from "@/hooks";
import {
  CreateDepartmentDef,
  CreateDepartmentSchema,
} from "@/models/validations/department/create-department.validation";
import { useCreateDepartmentMutation } from "@/services/department.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { InputField, TextareaField } from "../form-fields";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateDepartmentForm = () => {
  const dispatch = useAppDispatch();

  const [createDepartment, { isLoading: isCreatingRole, error, requestId }] =
    useCreateDepartmentMutation();

  const form = useForm<CreateDepartmentDef>({
    resolver: zodResolver(CreateDepartmentSchema),
    defaultValues: {},
    mode: "all",
  });

  const onSubmit = async (data: CreateDepartmentDef) => {
    try {
      const res = await createDepartment(data).unwrap();

      if (res.success) {
        toast("Department request submitted and is pending authorization");
        dispatch(modal.mutation.close());
      }
    } catch {
      if (error && typeof error === "object" && "data" in error) {
        const errData = error.data as { message?: string };
        toast(errData.message ?? "Something went wrong.", { id: requestId });
      } else {
        toast("Something went wrong. Try again!", { id: requestId });
      }
    }
  };

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="name"
              label="Name"
              placeholder="Enter department name"
            />
            {/* <ComboBoxField
              options={branches?.data || []}
              isLoading={isLoadingBranches}
              labelKey="name"
              valueKey="id"
              form={form}
              name="branchId"
              label="Branch"
              infoKey="taxJurisdiction"
              placeholder="Select branch"
            /> */}
            <TextareaField
              form={form}
              name="description"
              label="Description "
              placeholder="Enter department description"
            />
          </div>
          <Button className="w-full font-medium" disabled={isCreatingRole}>
            {isCreatingRole && <LoaderIcon className="animate-spin" />}
            Create Department
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default CreateDepartmentForm;
