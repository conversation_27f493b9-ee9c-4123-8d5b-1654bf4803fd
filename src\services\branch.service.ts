// services/authApi.ts

import { BranchResponseDef } from "@/models/response/branch";
import { DefaultResponse } from "@/models/response/default.response";
import { CreateBranchDef } from "@/models/validations/branch/create-branch.validation";
import { DeleteBranchDef } from "@/models/validations/branch/delete-branch.validation";
import { UpdateBranchDef } from "@/models/validations/branch/update-branch.validation";
import { baseService } from "./base.service";

export const branchService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createBranch: builder.mutation<DefaultResponse, CreateBranchDef>({
      query: (credentials) => ({
        url: "/branch",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    updateBranch: builder.mutation<DefaultResponse, UpdateBranchDef>({
      query: (credentials) => ({
        url: "/branch/update",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    deleteBranch: builder.mutation<DefaultResponse, DeleteBranchDef>({
      query: (credentials) => ({
        url: "/branch/delete",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getBranches: builder.query<
      DefaultResponse & { data: BranchResponseDef[] },
      void
    >({
      query: () => ({
        url: "/branch",
        method: "GET",
      }),
      providesTags: ["Branch"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateBranchMutation,
  useGetBranchesQuery,
  useUpdateBranchMutation,
  useDeleteBranchMutation,
} = branchService;
