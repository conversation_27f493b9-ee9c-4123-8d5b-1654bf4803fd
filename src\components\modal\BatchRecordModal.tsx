"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import BatchRecordTable from "../table/BatchRecordTable";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const BatchRecordModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Batch Records</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <BatchRecordTable />
    </>
  );
};

export default BatchRecordModal;
