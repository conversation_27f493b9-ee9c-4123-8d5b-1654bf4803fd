// import FilterIcon from "@/assets/icon/filter-icon.svg"
import { AgGridReact } from "ag-grid-react";
import React from "react";
import { SSSROptions } from "../BaseTable";
export interface DefaultHeaderComponentProps {
  pageSize?: number | string;
  setPageSize?: (value: string | number) => void;
  //   tabsOption: ButtonTabProps;
  gridRef?: React.RefObject<AgGridReact>;
  usedKeys?: {
    filter?: string;
    tab?: string;
  };
  ssr?: SSSROptions;
  type?: "table" | "list";
}

export const DefaultHeaderComponent = ({
  type = "table",
  ...props
}: DefaultHeaderComponentProps) => {
  console.log(type, props);

  // const dispatch = useDispatch<any>()

  return (
    <div className={"flex justify-between items-center gap-2 h-[110px]"}></div>
  );
};
