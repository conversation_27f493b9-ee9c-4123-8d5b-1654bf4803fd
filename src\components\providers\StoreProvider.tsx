import { persistor, store } from "@/store";
import { LoaderIcon } from "lucide-react";
import React, { useEffect, useState } from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";

export default function StoreProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [rehydrated, setRehydrated] = useState(false);

  useEffect(() => {
    const unsubscribe = persistor.subscribe(() => {
      if (persistor.getState().bootstrapped) {
        setRehydrated(true);
      }
    });
    return () => unsubscribe();
  }, []);

  if (!rehydrated) {
    return (
      <div className="h-screen w-full flex items-center justify-center">
        <LoaderIcon className="w-6 h-6 animate-spin" />
      </div>
    ); // Render a loading state
  }

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        {children}
      </PersistGate>
    </Provider>
  );
}
