import { useAppDispatch, useAppSelector } from "@/hooks";
import { cn } from "@/lib/utils";
import { RoleResponseDef } from "@/models/response/role";
import {
  UpdateRoleDef,
  UpdateRoleSchema,
} from "@/models/validations/role/update-role.validation";
import {
  useGetRoleByIdQuery,
  useUpdateRoleMutation,
} from "@/services/role.service";
import { modal } from "@/store/module/modal";
import { sheet } from "@/store/module/sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { skipToken } from "@reduxjs/toolkit/query";
import { LoaderIcon, PlusIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { InputField, TextareaField } from "../../form-fields";
import PrivilegeTable from "../../table/PrivilegeTable";
import { But<PERSON> } from "../../ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../ui/dialog";
import { Form } from "../../ui/form";

const UpdateRoleForm = () => {
  const dispatch = useAppDispatch();
  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);

  const {
    sheetOptions: { metadata: sheetMetadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = sheetMetadata as { data: RoleResponseDef };

  const { data: roleResponse, isLoading: isLoadingRoleDetails } =
    useGetRoleByIdQuery(data.id ? { roleId: data.id } : skipToken, {
      refetchOnMountOrArgChange: true,
    });

  const { selectedPrivilege } = metadata as {
    selectedPrivilege: Array<{
      name: string;
      id: number;
      status: string;
    }>;
  };

  const [open, setOpen] = useState(false);

  const [updateRole, { isLoading: isCreatingRole, error, requestId }] =
    useUpdateRoleMutation();

  const form = useForm<UpdateRoleDef>({
    resolver: zodResolver(UpdateRoleSchema),
    defaultValues: {
      id: data.id,
      privileges: [],
      description: data.description || "",
      name: data.name,
    },
    mode: "all",
  });

  const onSubmit = async (data: UpdateRoleDef) => {
    try {
      const res = await updateRole(data).unwrap();

      if (res.success) {
        toast.success("Role request submitted and is pending authorization", {
          id: requestId,
        });
        dispatch(sheet.mutation.close());
      }
    } catch {
      console.log(error);
    }
  };

  const onClose = () => {
    setOpen(false);
    form.trigger("privileges");
  };

  useEffect(() => {
    if (selectedPrivilege?.length > 0) {
      const privilegesName = selectedPrivilege.map((priv) => priv.name);
      form.setValue("privileges", privilegesName);
    }
  }, [form, selectedPrivilege]);

  useEffect(() => {
    if (roleResponse) {
      dispatch(
        modal.mutation.setMetadata({
          selectedPrivilege: roleResponse.data.privileges,
        })
      );
    }
  }, [roleResponse, dispatch]);

  const privileges = form.watch("privileges");

  console.log(form.formState.errors);

  return (
    <section className="min-[500px]:min-w-md p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="name"
              label="Title"
              placeholder="Enter role title"
            />
            <TextareaField
              form={form}
              name="description"
              label="Description "
              placeholder="Enter role description"
            />
            <Dialog
              open={open}
              onOpenChange={(isOpen) => {
                if (!isOpen) {
                  onClose();
                }
              }}
            >
              <DialogTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className={cn(
                    form.getFieldState("privileges").invalid && "text-red-500"
                  )}
                  onClick={() => setOpen(true)}
                >
                  <PlusIcon />
                  Add Permission
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add privileges</DialogTitle>
                </DialogHeader>
                <div className="">
                  <PrivilegeTable onCancel={onClose} />
                </div>
              </DialogContent>
            </Dialog>
            <p className="text-primary text-xs font-medium">{`${privileges?.length} privileges selected`}</p>
          </div>
          <Button
            className="w-full font-medium"
            disabled={isCreatingRole || isLoadingRoleDetails}
          >
            {isCreatingRole && <LoaderIcon className="animate-spin" />}
            Update Role
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default UpdateRoleForm;
