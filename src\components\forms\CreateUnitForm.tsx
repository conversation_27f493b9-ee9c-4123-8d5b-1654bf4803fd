import { useAppDispatch } from "@/hooks";
import {
  CreateUnitDef,
  CreateUnitSchema,
} from "@/models/validations/unit/create-unit.validation";
import { useGetRegionsQuery } from "@/services/region.service";
import { useGetTaxJurisdictionsQuery } from "@/services/tax-jurisdiction.service";
import { useCreateUnitMutation } from "@/services/unit.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { ComboBoxField, InputField, TextareaField } from "../form-fields";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateUnitForm = () => {
  const dispatch = useAppDispatch();

  const { data: taxJurisdictions, isLoading: isLoadingTaxJurisdictions } =
    useGetTaxJurisdictionsQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });

  const { data: regions, isLoading: isLoadingRegions } = useGetRegionsQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const [createUnit, { isLoading: isCreatingEmployee, error }] =
    useCreateUnitMutation();

  const form = useForm<CreateUnitDef>({
    resolver: zodResolver(CreateUnitSchema),
    defaultValues: {},
    mode: "all",
  });

  const onSubmit = async (data: CreateUnitDef) => {
    try {
      const res = await createUnit(data).unwrap();

      if (res.success) {
        toast("Unit request submitted and is pending authorization");
        dispatch(modal.mutation.close());
      }
    } catch {
      if (error && typeof error === "object" && "data" in error) {
        const errData = error.data as { message?: string };
        toast(errData.message ?? "Something went wrong.");
      } else {
        toast("Something went wrong. Try again!");
      }

      // toast(error)
    }
  };

  console.log(form.formState.errors);

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <>
              <InputField form={form} name="name" label="Unit name" />

              <ComboBoxField
                options={taxJurisdictions?.data || []}
                form={form}
                name="taxJurisdiction"
                label="Tax Jurisdiction"
                disabled={isLoadingTaxJurisdictions}
                isLoading={isLoadingTaxJurisdictions}
                placeholder="Select tax jurisdiction"
                labelKey="name"
                valueKey="name"
              />

              <ComboBoxField
                options={regions?.data || []}
                form={form}
                name="region"
                label="Region"
                disabled={isLoadingRegions}
                isLoading={isLoadingRegions}
                placeholder="Select region"
                labelKey="name"
                valueKey="name"
              />

              <TextareaField
                form={form}
                name="description"
                label="Description "
                placeholder="Enter unit description"
              />

              <div className="flex gap-4 justify-between">
                <Button className="font-medium" disabled={isCreatingEmployee}>
                  {isCreatingEmployee && (
                    <LoaderIcon className="animate-spin" />
                  )}
                  Create unit
                </Button>
              </div>
            </>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default CreateUnitForm;
