// services/authApi.ts

import { ContractTypeResponseDef } from "@/models/response/contract-type";
import { DefaultResponse } from "@/models/response/default.response";
import { NameDescriptionDef } from "@/models/validations";
import { UpdateContractTypeDef } from "@/models/validations/contract-type/update-contract-type.validation";
import { baseService } from "./base.service";

export const contractType = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createContractType: builder.mutation<DefaultResponse, NameDescriptionDef>({
      query: (credentials) => ({
        url: "/contract-type",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),

    updateContractType: builder.mutation<
      DefaultResponse,
      UpdateContractTypeDef
    >({
      query: (credentials) => ({
        url: "/contract-type/update",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),

    deleteContractType: builder.mutation<DefaultResponse, { id: string }>({
      query: (credentials) => ({
        url: "/contract-type/delete",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),

    getContractTypes: builder.query<
      DefaultResponse & { data: ContractTypeResponseDef[] },
      void
    >({
      query: () => ({
        url: "/contract-type",
        method: "GET",
      }),
      providesTags: ["Contracts"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateContractTypeMutation,
  useGetContractTypesQuery,
  useUpdateContractTypeMutation,
  useDeleteContractTypeMutation,
} = contractType;
