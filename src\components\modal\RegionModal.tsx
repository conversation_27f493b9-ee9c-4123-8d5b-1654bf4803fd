"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateRegionForm from "../forms/CreateRegionForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const RegionModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new region</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateRegionForm />
    </>
  );
};

export default RegionModal;
