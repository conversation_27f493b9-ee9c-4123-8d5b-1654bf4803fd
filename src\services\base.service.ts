// services/baseApi.ts
import { appConfig } from "@/config/app.config";
import { ModalConstant } from "@/constants/ModalConstant";
import { modal } from "@/store/module/modal";
import {
  BaseQueryFn,
  createApi,
  FetchArgs,
  fetchBaseQuery,
} from "@reduxjs/toolkit/query/react";
import { toast } from "sonner";

const baseQuery = fetchBaseQuery({
  baseUrl:
    window.location.protocol.toLowerCase() === "https:"
      ? appConfig.baseUrlProd
      : appConfig.baseUrlDev,
  prepareHeaders: (headers, { getState }) => {
    const token = (getState() as { auth?: { token?: string } })?.auth?.token;

    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }

    headers.set("Content-Type", "application/json");
    headers.set("Accept", "application/json");

    return headers;
  },
});

// 🛡️ Wrap it to catch 401 errors
const baseQueryWith401Handler: BaseQueryFn<
  string | FetchArgs,
  unknown,
  unknown
> = async (args, api, extraOptions) => {
  const result = await baseQuery(args, api, extraOptions);

  if (result?.error) {
    if (result.error.status === 401) {
      api.dispatch(
        modal.mutation.open({
          modalType: ModalConstant.sessionExpiredModal,
        })
      );
    } else {
      console.log(result);

      toast.error(
        (result?.error?.data as { message?: string })?.message ||
          "Something went wrong",
        { className: "!border !border-[#F4C8CF] !bg-[#F9E1E5] !text-[#AF233A]" }
      );
    }
  }

  return result;
};

export const baseService = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWith401Handler,
  tagTypes: [
    "Account",
    "Employees",
    "EmpBatch",
    "Roles",
    "Regions",
    "Users",
    "Dashboard",
    "Salary",
    "Loan",
    "Privileges",
    "Authorization",
    "EmployeesGroup",
    "Branch",
    "Grades",
    "Departments",
    "Units",
    "Contracts",
    "Designations",
    "Company",
    "Locations",
    "SalaryPackages",
    "Allowances",
    "TaxJurisdictions",
    "Deductions",
    "Payroll",
    "DashboardAnalysis",
    "SubBranches",
  ],
  endpoints: () => ({}), // Define endpoints elsewhere or later
});
