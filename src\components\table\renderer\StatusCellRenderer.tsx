import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CustomCellRendererProps } from "ag-grid-react";
import { cva } from "class-variance-authority";

export const StatusCellRenderer = ({
  value, // The actual status value from the column
}: CustomCellRendererProps<object>) => {
  const statusVariant = cva(
    " text-xs font-semibold rounded-md !hover:bg-transparent !hover:border-current !hover:text-current pointer-events-none",
    {
      variants: {
        variant: {
          default: "border border-[#D1D5DB] bg-[#F3F4F6] text-[#374151]",
          success: "border border-[#C0E7D0] bg-[#D6F0E0] text-[#0D6832]",
          destructive: "border border-[#F4C8CF] bg-[#F9E1E5] text-[#AF233A]",
          warning: "border border-[#F9E4BE] bg-[#FBF0DA] text-[#73510D]",
        },
      },
      defaultVariants: {
        variant: "default",
      },
    }
  );

  // Determine the status class based on value
  const getStatusVariant = (status: string | undefined) => {
    if (!status) return statusVariant({ variant: "default" });

    switch (status.toLowerCase()) {
      case "active":
      case "success":
      case "approved":
      case "completed":
        return statusVariant({ variant: "success" });

      case "inactive":
      case "destructive":
      case "failed":
      case "declined":
      case "rejected":
        return statusVariant({ variant: "destructive" });

      case "pending":
      case "creating":
      case "warning":
        return statusVariant({ variant: "warning" });

      default:
        return statusVariant({ variant: "default" });
    }
  };

  return (
    <Button
      className={cn(
        "!text-[10px] h-5 max-w-28 px-2 w-full ",
        getStatusVariant(value)
      )}
      size="sm"
    >
      {value || "N/A"}
    </Button>
  );
};
