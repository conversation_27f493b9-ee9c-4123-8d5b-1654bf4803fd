// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { GradeResponseDef } from "@/models/response/grade";
import { CreateGradeDef } from "@/models/validations/grade/create-grade.validation";
import { baseService } from "./base.service";

export const gradeService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createGrade: builder.mutation<DefaultResponse, CreateGradeDef>({
      query: (credentials) => ({
        url: "/grade",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    updateGrade: builder.mutation<DefaultResponse, CreateGradeDef>({
      query: (credentials) => ({
        url: "/grade/update",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    deleteGrade: builder.mutation<DefaultResponse, { id: string }>({
      query: (credentials) => ({
        url: "/grade/delete",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getGrade: builder.query<
      DefaultResponse & { data: GradeResponseDef[] },
      void
    >({
      query: () => ({
        url: "/grade",
        method: "GET",
      }),
      providesTags: ["Grades"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateGradeMutation,
  useGetGradeQuery,
  useDeleteGradeMutation,
  useUpdateGradeMutation,
} = gradeService;
