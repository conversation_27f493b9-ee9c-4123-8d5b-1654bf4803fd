import { ModalConstant } from '@/constants/ModalConstant';
import { SheetConstant } from '@/constants/SheetConstant';
import { useAppDispatch } from '@/hooks';
import { PayrollUploadResponseDef } from '@/models/response/payroll/read-payroll.response';
import { useGetPayrollUploadsQuery } from '@/services/payroll.service';
import { modal } from '@/store/module/modal';
import { sheet } from '@/store/module/sheet';
import { ColDef } from 'ag-grid-community';
import { EyeIcon, LayersIcon, SendIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import BaseTable from './BaseTable';

const PayrollTable = () => {
  const { data, isLoading, error, requestId } = useGetPayrollUploadsQuery(undefined, {
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  });

  if (error && typeof error === 'object' && 'data' in error) {
    const errData = error.data as { message?: string };
    toast(errData.message ?? 'Something went wrong.', { id: requestId });
  }

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<PayrollUploadResponseDef>[]>([
    {
      headerName: 'Period',
      field: 'period',
    },
    {
      headerName: 'Uploaded By',
      field: 'uploadedBy',
    },
    {
      headerName: 'Approved By',
      field: 'approvedBy',
    },
    {
      headerName: 'Status',
      field: 'status',
    },
  ]);

  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={data?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        showDefault: true,
        actions: [
          {
            title: 'Disburse',
            Icon: SendIcon,
            onClick: node => {
              dispatch(
                modal.mutation.open({
                  modalType: ModalConstant.disbursePayrollRecordModal,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
            className: node => (node.data.status === 'DISBURSED' ? 'hidden' : ''),
          },
          {
            title: 'View Record',
            Icon: EyeIcon,
            onClick: node => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.payrollRecordSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
          {
            title: 'View Batch',
            Icon: LayersIcon,
            onClick: node => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.payrollBatchSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default PayrollTable;
