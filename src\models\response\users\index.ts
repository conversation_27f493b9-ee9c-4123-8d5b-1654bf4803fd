export type UserResponseDef = {
  id: string;
  name: string;
  email: string;
  password: string;
  twoFactorEnabled: boolean;
  isRoot: boolean;
  hasAccessToAllBranches: boolean;
  branchId: number | null;
  companyId: number;
  roleId: number;
  role: { name: string };
  branches: { name: string; id: string }[];
  createdBy: string;
  approvedBy: number | null;
  status: string;
  createdAt: string;
  updatedAt: string;
};
