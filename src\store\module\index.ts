/* eslint-disable @typescript-eslint/no-explicit-any */
import { baseService } from "@/services/base.service";
import { combineReducers } from "@reduxjs/toolkit";
import { account } from "./account";
import { auth } from "./auth";
import { company } from "./company";
import { modal } from "./modal";
import { sheet } from "./sheet";

const appReducers = combineReducers({
  [baseService.reducerPath]: baseService.reducer,
  auth: auth.reducer,
  modal: modal.reducer,
  sheet: sheet.reducer,
  company: company.reducer,
  account: account.reducer,
});

const rootReducer = (state: any, action: any) => {
  if (action.type === "RESET_STATE") {
    state = undefined; // Reset all state on logout
  }
  return appReducers(state, action);
};

export const resetState = () => ({ type: "RESET_STATE" });

export type RootReducerState = ReturnType<typeof rootReducer>;

export default rootReducer;
