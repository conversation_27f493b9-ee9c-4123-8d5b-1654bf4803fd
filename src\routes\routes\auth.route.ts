import { RouteConstant } from "@/constants/RouteConstant";
import { OnboardingView } from "@/view/auth";
import ForgotPasswordView from "@/view/auth/ForgotPasswordView";
import LoginView from "@/view/auth/LoginView";
import NewPasswordView from "@/view/auth/NewPasswordView";
import { RouteDef } from ".";

export const authRoute: RouteDef[] = [
  {
    path: RouteConstant.auth.login,
    Component: LoginView,
    metadata: {},
  },
  {
    path: RouteConstant.auth.createAccount,
    Component: OnboardingView,
    metadata: {},
  },
  {
    path: RouteConstant.auth.resetPassword,
    Component: NewPasswordView,
    metadata: {},
  },
  {
    path: RouteConstant.auth.forgetPassword,
    Component: ForgotPasswordView,
    metadata: {},
  },
];
