import { MenuIcon } from "lucide-react";
import Logo from "../shared/Logo";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import {
  Sheet,
  Sheet<PERSON>lose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "../ui/sheet";

const navLinks = [
  {
    title: "Account",
    icon: "/icons/user.svg",
  },
  {
    title: "Cart",
    icon: "/icons/cart.svg",
  },
  {
    title: "Wishlist",
    icon: "/icons/heart.svg",
  },
  {
    title: "Support",
    icon: "/icons/support.svg",
  },
];

const Navbar = () => {
  return (
    <header className="flex items-center justify-between w-full h-20">
      <div className="wrapper flex items-center justify-between gap-10">
        <Logo />

        <div className="flex flex-1 items-center gap-6">
          <div className="flex-1 flex items-center gap-2 max-md:hidden">
            <Input className="h-10" />
            <Button className="h-10">Search</Button>
          </div>
          <nav className="shrink-0 max-lg:hidden">
            {navLinks.map(({ icon, title }) => (
              <Button size="sm" variant="ghost" className="gap-0.5 font-medium">
                <img src={icon} alt="icon" className="text-[0px] w-4 h-4" />
                {title}
              </Button>
            ))}
          </nav>
        </div>
        <Sheet>
          <SheetTrigger className="lg:hidden" asChild>
            <Button variant="ghost" className="w-8 h-8 !px-0 rounded-full">
              <MenuIcon className="!w-6 !h-6 text-primary" />
            </Button>
          </SheetTrigger>
          <SheetContent>
            <SheetHeader>
              <SheetTitle>Menu</SheetTitle>
            </SheetHeader>
            <SheetClose asChild>
              <nav className="flex flex-col gap-4">
                {navLinks.map(({ icon, title }) => (
                  <Button
                    size="sm"
                    variant="ghost"
                    className="gap-0.5 font-medium justify-start"
                  >
                    <img src={icon} alt="icon" className="text-[0px] w-4 h-4" />
                    {title}
                  </Button>
                ))}
              </nav>
            </SheetClose>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  );
};

export default Navbar;
