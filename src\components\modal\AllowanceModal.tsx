"use client";

import { useAppSelector } from "@/hooks/index.ts";
import { GradeLevelResponseDef } from "@/models/response/grade-level/index.ts";
import { DialogTitle } from "@radix-ui/react-dialog";
import CreateAllowanceForm from "../forms/CreateAllowanceForm.tsx";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const AllowanceModal = () => {
  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);

  const {
    data: { name },
  } = metadata as { data: GradeLevelResponseDef };

  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new allowance</DialogTitle>
        <DialogDescription className="max-w-sm">
          {`Add new allowance for "${name}" grade level`}
        </DialogDescription>
      </DialogHeader>
      <CreateAllowanceForm />
    </>
  );
};

export default AllowanceModal;
