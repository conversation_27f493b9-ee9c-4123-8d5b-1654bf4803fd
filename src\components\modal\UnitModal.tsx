"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateUnitForm from "../forms/CreateUnitForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const UnitModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new unit</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateUnitForm />
    </>
  );
};

export default UnitModal;
