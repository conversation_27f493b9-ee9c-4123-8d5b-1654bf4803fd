import { z } from "zod";

export const CreateEmployeeGroupSchema = z.object({
  name: z.string().min(1, "Required"),
  netSalary: z.string(),
  grossSalary: z.string().min(1, "Required"),
  tax: z.string().min(1, "Required"),
  otherDeductions: z.string().optional(),
  currency: z.string(),
  jobGradeId: z.number({ required_error: "Required" }),
});

export type CreateEmployeeGroupDef = z.infer<typeof CreateEmployeeGroupSchema>;
