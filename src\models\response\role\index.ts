export type RoleResponseDef = {
  id: number;
  name: string;

  description: string | null;
  isPublic: boolean;
  companyId: number | null;
  createdBy: string;
  approvedBy: string;

  status: string;
  createdAt: string;
  updatedAt: string;
};

export type RoleDetailsResponseDef = {
  id: number;
  name: string;

  description: string | null;
  isPublic: boolean;
  companyId: number | null;
  createdBy: string;
  approvedBy: string;

  status: string;
  createdAt: string;
  updatedAt: string;

  privileges: {
    privilegeName: "EMPLOYEE|CREATE";
    roleId: 105;
    assignedAt: "2025-06-10T09:34:38.799Z";
  }[];
};
