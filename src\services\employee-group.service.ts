// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { GradeLevelResponseDef } from "@/models/response/grade-level";
import { CreateGradeLevelDef } from "@/models/validations/grade-level/create-grade-level.validation";
import { baseService } from "./base.service";

export const gradeLevelService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createGradeLevel: builder.mutation<DefaultResponse, CreateGradeLevelDef>({
      query: (credentials) => ({
        url: "/employee-group",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["EmployeesGroup"],
    }),
    updateGradeLevel: builder.mutation<DefaultResponse, CreateGradeLevelDef>({
      query: (credentials) => ({
        url: "/employee-group/update",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["EmployeesGroup"],
    }),
    deleteGradeLevel: builder.mutation<DefaultResponse, { id: string }>({
      query: (credentials) => ({
        url: "/employee-group/delete",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["EmployeesGroup"],
    }),
    getGradeLevels: builder.query<
      DefaultResponse & { data: GradeLevelResponseDef[] },
      void
    >({
      query: () => ({
        url: "/employee-group",
        method: "GET",
      }),
      providesTags: ["EmployeesGroup"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateGradeLevelMutation,
  useGetGradeLevelsQuery,
  useUpdateGradeLevelMutation,
  useDeleteGradeLevelMutation,
} = gradeLevelService;
