import { RouteConstant } from "@/constants/RouteConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { modal } from "@/store/module/modal";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import { DialogTitle } from "@radix-ui/react-dialog";
import { Button } from "../ui/button";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const AccountCreatedSuccessModal = () => {
  const dispatch = useAppDispatch();

  const location = window.location;

  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);

  const handleRedirect = () => {
    const subdomain = metadata?.slug;

    if (!subdomain) {
      console.error("Missing subdomain slug.");
      return;
    }

    const isLocalhost = location.hostname === "localhost";
    const loginPath = RouteConstant.auth.login;

    const targetUrl = isLocalhost
      ? `${location.protocol}//${subdomain}.${location.host}/${loginPath}`
      : `https://${subdomain}.${location.host}${loginPath}`;

    console.log("Redirecting to:", targetUrl);
    window.location.replace(targetUrl);
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle className="flex items-center justify-center flex-col">
          <DotLottieReact
            src="https://lottie.host/65193d24-c450-4aa5-b152-bd70536c325a/asUhOsCNNr.lottie"
            autoplay
          />
          <p className="font-bold mt-2 text-3xl max-w-sm text-center">
            Welcome, your account has been{" "}
            <span className="text-brand">created!</span>
          </p>
        </DialogTitle>
        <DialogDescription className="max-w-sm text-center">
          Congratulations! Your account has been successfully created. You're
          all set to start.
        </DialogDescription>
      </DialogHeader>

      <div className="flex items-center justify-center mt-5">
        <div className="mt-4 flex justify-center gap-3">
          <Button
            onClick={() => {
              dispatch(modal.mutation.close());
              handleRedirect();
            }}
          >
            Login and proceed to dashboard
          </Button>
        </div>
      </div>
    </>
  );
};

export default AccountCreatedSuccessModal;
