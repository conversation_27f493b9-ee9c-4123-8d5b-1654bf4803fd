import { Input<PERSON>ield, TextareaField } from "@/components/form-fields";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { GradeResponseDef } from "@/models/response/grade";
import {
  UpdateGradeDef,
  UpdateGradeSchema,
} from "@/models/validations/grade/update-grade.validation";
import { useUpdateGradeMutation } from "@/services/grade.service";
import { sheet } from "@/store/module/sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const UpdateGradeForm = () => {
  const dispatch = useAppDispatch();

  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: GradeResponseDef };

  const [updateGrade, { isLoading: isUpdatingGrade, error, requestId }] =
    useUpdateGradeMutation();

  const form = useForm<UpdateGradeDef>({
    resolver: zodResolver(UpdateGradeSchema),
    defaultValues: {
      description: data.description,
      id: data.id,
      name: data.name,
    },
    mode: "all",
  });

  const onSubmit = async (data: UpdateGradeDef) => {
    try {
      const res = await updateGrade(data).unwrap();

      if (res.success) {
        toast(`Cadre request submitted and is pending authorization`);
        dispatch(sheet.mutation.close());
      }
    } catch {
      if (error && typeof error === "object" && "data" in error) {
        const errData = error.data as { message?: string };
        toast(errData.message ?? "Something went wrong.", { id: requestId });
      } else {
        toast("Something went wrong. Try again!", { id: requestId });
      }
    }
  };

  return (
    <section className="mx-auto p-4 w-full">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="name"
              label="Title"
              placeholder={`Enter cadre title`}
            />
            <TextareaField
              form={form}
              name="description"
              label="Description "
              placeholder="Enter description"
            />
          </div>
          <Button className="w-full font-medium" disabled={isUpdatingGrade}>
            {isUpdatingGrade && <LoaderIcon className="animate-spin" />}
            {`Update cadre`}
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default UpdateGradeForm;
