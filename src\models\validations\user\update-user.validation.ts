import { z } from "zod";

export const UpdateUserSchema = z.object({
  name: z.string().min(1, "Required"),
  email: z.string().email({
    message: "Invalid email address",
  }),
  id: z.string(),
  branches: z.array(z.string()).optional(),
  roleId: z.number().int(),
  twoFactorEnabled: z.boolean().optional(),
  hasAccessToAllBranches: z.boolean().optional(),
});

export type UpdateUserDef = z.infer<typeof UpdateUserSchema>;
