/* eslint-disable @typescript-eslint/no-explicit-any */
// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { PayrollBatchResponseDef } from "@/models/response/payroll/read-payroll-batch.response";
import { PayrollUploadResponseDef } from "@/models/response/payroll/read-payroll.response";
// import { CreatePayrollRecordDef } from "@/models/validations/payroll/create-payroll-record.validation";
import { ReadPayrollRecordResponse } from "@/models/response/payroll/read-payroll-record.response";
import { CreatePayrollUploadDef } from "@/models/validations/payroll/create-payroll-upload.validation";
import { baseService } from "./base.service";

export const payrollService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createPayroll: builder.mutation<
      DefaultResponse,
      CreatePayrollUploadDef & { records: any[] }
    >({
      query: (credentials) => ({
        url: "/payroll/create",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Payroll"],
    }),
    disbursePayroll: builder.mutation<DefaultResponse, { id: string }>({
      query: (payload) => ({
        url: "/payroll/update/disburse",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getPayrollUploads: builder.query<
      DefaultResponse & { data: PayrollUploadResponseDef[] },
      void
    >({
      query: () => ({
        url: "/payroll/read",
        method: "GET",
      }),
      providesTags: ["Payroll"],
    }),
    getPayrollRecords: builder.query<
      DefaultResponse & { data: ReadPayrollRecordResponse[] },
      { payrollUploadId: string }
    >({
      query: (payload) => ({
        url: `/payroll/read/payroll-record-by-payroll-upload-id/${payload.payrollUploadId}`,
        method: "GET",
      }),
      providesTags: (__, _, { payrollUploadId }) => [
        { type: "Payroll", id: `record-${payrollUploadId}` },
      ],
    }),
    getPayrollBatch: builder.query<
      DefaultResponse & { data: PayrollBatchResponseDef[] },
      { payrollUploadId: string }
    >({
      query: (payload) => ({
        url: `/payroll/read/payroll-batch-by-payroll-upload-id/${payload.payrollUploadId}`,
        method: "GET",
      }),
      providesTags: (__, _, { payrollUploadId }) => [
        { type: "Payroll", id: `batch-${payrollUploadId}` },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreatePayrollMutation,
  useGetPayrollUploadsQuery,
  useGetPayrollBatchQuery,
  useGetPayrollRecordsQuery,
  useDisbursePayrollMutation,
} = payrollService;
