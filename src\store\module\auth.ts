import { createSlice } from "@reduxjs/toolkit";

export type AuthState = {
  token: string | null;
  error?: string;
  isLoading: boolean;
  isEmailVerified: boolean;
  //   loginInfo: LoginRequestDef;
  account: {
    userEmail: string;
    userFullName: string;
    userId: string | null;
    userPhoneNumber: string;
    userRoleId: number;
    userStatus: string;
    isRoot: boolean;
    accountId: number;
  };
};

const initialState: AuthState = {
  isLoading: false,
  isEmailVerified: false,
  token: "",
  //   loginInfo: LoginRequestDefInit,
  account: {
    accountId: 0,
    userEmail: "",
    userFullName: "",
    userId: null,
    userPhoneNumber: "",
    userRoleId: 0,
    userStatus: "",
    isRoot: false,
  },
};

// All services for onboarding

const slice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setLoading(state, { payload }) {
      state.isLoading = payload;
    },
    setIsEmailVerified(state, { payload }) {
      state.isEmailVerified = payload;
    },

    setAccount(state, { payload }: { payload: Partial<AuthState["account"]> }) {
      state.account = { ...state.account, ...payload };
    },
    setToken(state, { payload }) {
      state.token = payload;
    },
  },
});

export const auth = {
  reducer: slice.reducer,
  mutation: slice.actions,
};
