import { useAppDispatch, useAppSelector } from "@/hooks";
import {
  CreateUserDef,
  CreateUserSchema,
} from "@/models/validations/user/create-user.validation";
import { useGetBranchesQuery } from "@/services/branch.service";
import { useGetRolesQuery } from "@/services/role.service";
import { useCreateUserMutation } from "@/services/user.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { CheckBoxField, ComboBoxField, InputField } from "../form-fields";
import MultiSelectField from "../form-fields/MultiSelectField";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateUserForm = () => {
  const dispatch = useAppDispatch();

  const { account } = useAppSelector((state) => state.auth);

  console.log(account);

  const [createUser, { isLoading: isCreatingUser }] = useCreateUserMutation();

  const { data: roleRes, isLoading: isLoadingRoles } = useGetRolesQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  // const { data: locationRes, isLoading: isLoadingLocation } =
  //   useGetLocationsQuery(undefined, {
  //     refetchOnMountOrArgChange: true,
  //   });

  const { data: branchRes, isLoading: isLoadingBranches } = useGetBranchesQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const form = useForm<CreateUserDef>({
    resolver: zodResolver(CreateUserSchema),
    defaultValues: {},
    mode: "all",
  });

  const onSubmit = async (data: CreateUserDef) => {
    try {
      const { branches, ...rest } = data;
      const payload: CreateUserDef =
        branches && branches.length > 0
          ? { ...rest, branches } as CreateUserDef
          : { ...rest } as CreateUserDef;

      const res = await createUser(payload).unwrap();
      if (res.success) {
        toast("User request submitted and is pending authorization");
        dispatch(modal.mutation.close());
      }
    } catch (error: unknown) {
      const errData = error as { data?: { message?: string } };
      toast.error(errData.data?.message);
    }
  };
  const hasAccessToAllBranches = form.watch("hasAccessToAllBranches");
  // const regionalUser = form.watch("regionalUser");

  useEffect(() => {
    if (hasAccessToAllBranches) {
      form.setValue("branches", []);
    }
  }, [hasAccessToAllBranches, form]);

  console.log(form.formState.errors);

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="name"
              label="Name"
              placeholder="Enter name"
            />
            <InputField
              form={form}
              name="email"
              label="Email"
              placeholder="Enter email"
            />
            <InputField
              form={form}
              name="password"
              label="Password"
              placeholder="Enter password"
              type="password"
            />
            <ComboBoxField
              options={
                roleRes?.data
                  ? roleRes?.data.map((role) => ({
                      value: role.id,
                      label: role.name,
                    }))
                  : []
              }
              form={form}
              name="roleId"
              label="Role"
              placeholder="Select Role"
              isLoading={isLoadingRoles}
            />

            <MultiSelectField
              options={
                branchRes?.data
                  ? branchRes?.data.map((branch) => ({
                      value: branch.id,
                      label: branch.name,
                    }))
                  : []
              }
              form={form}
              disabled={hasAccessToAllBranches}
              name="branches"
              label="Branch"
              placeholder="Select Branch"
              isLoading={isLoadingBranches}
              description="Multiple Select is allowed"
            />
            {/* <CheckBoxField
              form={form}
              name="regionalUser"
              label="Regional User"
            /> */}
            {/* {regionalUser && (
              <ComboBoxField
                options={
                  locationRes?.data
                    ? locationRes?.data.map((location) => ({
                        value: location.id,
                        label: location.name,
                      }))
                    : []
                }
                form={form}
                name="locations"
                label="Location"
                placeholder="Select Location"
                isLoading={isLoadingLocation}
              />
            )} */}
            <CheckBoxField
              form={form}
              name="hasAccessToAllBranches"
              label="Has Access to all branches"
            />
          </div>
          <Button className="w-full font-medium" disabled={isCreatingUser}>
            {isCreatingUser && <LoaderIcon className="animate-spin" />}
            Create User
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default CreateUserForm;
