import { useAppDispatch, useAppSelector } from "@/hooks";
import { useDisbursePayrollMutation } from "@/services/payroll.service";
import { modal } from "@/store/module/modal";
import { CheckCircle2Icon } from "lucide-react";
import { toast } from "sonner";
import { Button } from "../ui/button";
import { DialogClose, DialogHeader, DialogTitle } from "../ui/dialog";

const DisbursePayrollModal = () => {
  const dispatch = useAppDispatch();
  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);

  const [disbursePayroll, { isLoading }] = useDisbursePayrollMutation();

  const { id, period } = metadata?.data as {
    id: string;
    period: string;
  };

  const onDisburseClick = async () => {
    console.log("Disbursing payroll with ID:", id);
    const res = await disbursePayroll({ id }).unwrap();
    if (res.success) {
      toast.success("Request successful and pending authorization");
      dispatch(modal.mutation.close());
    }
  };

  const title = `Confirm Disbursement for ${period}`;
  const description =
    "This action will mark the payroll as disbursed. Are you sure you want to continue?";

  return (
    <div className="min-w-80 py-4">
      <DialogHeader>
        <DialogTitle className="font-medium text-center text-base">
          {title}
        </DialogTitle>
      </DialogHeader>

      <div className="mt-4">
        <p className="text-center text-sm text-neutral-500">{description}</p>
      </div>

      <div className="mt-5 flex items-center justify-center gap-4">
        <DialogClose>
          <Button variant="outline" size="sm" disabled={isLoading}>
            Cancel
          </Button>
        </DialogClose>
        <Button
          onClick={onDisburseClick}
          variant="default"
          size="sm"
          disabled={isLoading}
        >
          <CheckCircle2Icon className="w-4 h-4 mr-1" />
          Disburse
        </Button>
      </div>
    </div>
  );
};

export default DisbursePayrollModal;
