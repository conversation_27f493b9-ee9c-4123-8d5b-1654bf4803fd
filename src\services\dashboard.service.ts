// services/authApi.ts

import { DashboardResponseDef } from "@/models/response/dashboard";
import { DefaultResponse } from "@/models/response/default.response";
import { baseService } from "./base.service";

export const dashboardService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    getAnalysis: builder.query<
      DefaultResponse & { data: DashboardResponseDef },
      void
    >({
      query: () => ({
        url: "/dashboard/read-analytics",
        method: "GET",
      }),
      providesTags: ["DashboardAnalysis"],
    }),
  }),
  overrideExisting: false,
});

export const { useGetAnalysisQuery } = dashboardService;
