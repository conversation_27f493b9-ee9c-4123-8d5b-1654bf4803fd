import { DeleteEntityConstant } from "@/constants/DeleteEntityConstant";
import { ModalConstant } from "@/constants/ModalConstant";
import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { DesignationResponseDef } from "@/models/response/designation";
import { useGetDesignationsQuery } from "@/services/designation.service";
import { modal } from "@/store/module/modal";
import { sheet } from "@/store/module/sheet";
import { ColDef, GridApi, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const DesignationTable = () => {
  const { data: response, isLoading } = useGetDesignationsQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<DesignationResponseDef>[]>([
    {
      headerName: "Department Name",
      field: "name",
    },
    {
      headerName: "Description",
      field: "description",
    },
    {
      headerName: "Employees count",
      field: "_count.employee",
    },
    {
      headerName: "Created by",
      field: "createdBy",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  const onEditClick = (node: IRowNode<DesignationResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.updateDesignationSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };

  const onDeleteClick = async ({
    node,
  }: {
    node: IRowNode<DesignationResponseDef>;
    api: GridApi;
  }) => {
    if (!node.data) {
      toast.error(`Designaton not found"`);
    }
    dispatch(
      modal.mutation.open({
        open: true,
        modalType: ModalConstant.confirmationModal,
        metadata: {
          id: node.data?.id,
          entity: DeleteEntityConstant.designation,
          title: `Delete "${node.data?.name}" designaton?`,
          description: `Are you sure you want to delete this designation "${node.data?.name}"`,
          warning: `By deleting this designation, the associated record will lose access to it.`,
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={response?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        defaultDeleteAction: onDeleteClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default DesignationTable;
