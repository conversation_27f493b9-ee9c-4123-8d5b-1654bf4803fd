import { DeleteEntityConstant } from "@/constants/DeleteEntityConstant";
import { ModalConstant } from "@/constants/ModalConstant";
import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { ContractTypeResponseDef } from "@/models/response/contract-type";
import { useGetContractTypesQuery } from "@/services/contract-type.service";
import { modal } from "@/store/module/modal";
import { sheet } from "@/store/module/sheet";
import { ColDef, GridApi, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const ContractTypeTable = () => {
  const { data: response, isLoading } = useGetContractTypesQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<ContractTypeResponseDef>[]>([
    {
      headerName: "Name",
      field: "name",
    },
    {
      headerName: "Description",
      field: "description",
    },
    {
      headerName: "Employees count",
      field: "_count.employees",
    },
    {
      headerName: "Created by",
      field: "createdBy",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  const onEditClick = (node: IRowNode<ContractTypeResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.updateContractTypeSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };

  const onDeleteClick = async ({
    node,
  }: {
    node: IRowNode<ContractTypeResponseDef>;
    api: GridApi;
  }) => {
    if (!node.data) {
      toast.error(`Employment Type not found"`);
    }
    dispatch(
      modal.mutation.open({
        open: true,
        modalType: ModalConstant.confirmationModal,
        metadata: {
          id: node.data?.id,
          entity: DeleteEntityConstant.contractType,
          title: `Delete "${node.data?.name}" Employment Type?`,
          description: `Are you sure you want to delete this Employment Type "${node.data?.name}"`,
          warning: `By deleting this Employment Type, the associated record will lose access to it.`,
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={response?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        defaultDeleteAction: onDeleteClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default ContractTypeTable;
