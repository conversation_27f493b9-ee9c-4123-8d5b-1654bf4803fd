import {
  <PERSON>mboBox<PERSON>ield,
  InputField,
  TextareaField,
} from "@/components/form-fields";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { BranchResponseDef } from "@/models/response/branch";
import {
  UpdateBranchDef,
  UpdateBranchSchema,
} from "@/models/validations/branch/update-branch.validation";
import { useUpdateBranchMutation } from "@/services/branch.service";
import { useGetRegionsQuery } from "@/services/region.service";
import { useGetTaxJurisdictionsQuery } from "@/services/tax-jurisdiction.service";
import { sheet } from "@/store/module/sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const UpdateBranchForm = () => {
  const dispatch = useAppDispatch();

  const {
    sheetOptions: { metadata: sheetMetadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = sheetMetadata as { data: BranchResponseDef };

  const [updateBranch, { isLoading: isCreatingEmployee, error }] =
    useUpdateBranchMutation();

  const { data: locations, isLoading: isLoadingLocations } =
    useGetTaxJurisdictionsQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });

  const { data: regions, isLoading: isLoadingRegions } = useGetRegionsQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const form = useForm<UpdateBranchDef>({
    resolver: zodResolver(UpdateBranchSchema),
    defaultValues: {
      description: data.description,
      id: data.id,
      taxJurisdiction: locations?.data.find(
        (lctn) => lctn.name === data.taxJurisdiction
      )?.name,
      region: regions?.data.find((region) => region.name === data.region)?.name,
      name: data.name,
    },
    mode: "all",
  });

  const onSubmit = async (data: UpdateBranchDef) => {
    try {
      const res = await updateBranch(data).unwrap();

      if (res.success) {
        toast("Update branch request submitted and is pending authorization");
        dispatch(sheet.mutation.close());
      }
    } catch {
      if (error && typeof error === "object" && "data" in error) {
        const errData = error.data as { message?: string };
        toast(errData.message ?? "Something went wrong.");
      } else {
        toast("Something went wrong. Try again!");
      }

      // toast(error)
    }
  };

  console.log(form.formState.errors);

  return (
    <section className="mx-auto w-full p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <>
              <InputField form={form} name="name" label="Name" />
              <ComboBoxField
                options={locations?.data || []}
                form={form}
                name="taxJurisdiction"
                label="Tax Jurisdiction"
                disabled={isLoadingLocations}
                isLoading={isLoadingLocations}
                placeholder="Select tax jurisdiction"
                labelKey="name"
                valueKey="name"
              />
              <ComboBoxField
                options={regions?.data || []}
                form={form}
                name="region"
                label="Region"
                disabled={isLoadingRegions}
                isLoading={isLoadingRegions}
                placeholder="Select region"
                labelKey="name"
                valueKey="name"
              />
              <TextareaField
                form={form}
                name="description"
                label="Description"
              />

              <div className="flex gap-4 justify-between">
                <Button className="font-medium" disabled={isCreatingEmployee}>
                  {isCreatingEmployee && (
                    <LoaderIcon className="animate-spin" />
                  )}
                  Update Branch/Location
                </Button>
              </div>
            </>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default UpdateBranchForm;
