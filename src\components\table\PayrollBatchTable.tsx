import { useAppSelector } from "@/hooks";
import { DecodedPayrollBatchResponseDef } from "@/models/response/payroll/read-payroll-batch.response";
import { PayrollUploadResponseDef } from "@/models/response/payroll/read-payroll.response";
import { useGetPayrollBatchQuery } from "@/services/payroll.service";
import { skipToken } from "@reduxjs/toolkit/query";
import { ColDef } from "ag-grid-community";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const PayrollBatchTable = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data: selectedPayroll } = metadata as {
    data?: PayrollUploadResponseDef;
  };

  const { data, isLoading, error, requestId } = useGetPayrollBatchQuery(
    selectedPayroll?.id ? { payrollUploadId: selectedPayroll.id } : skipToken,
    {
      refetchOnMountOrArgChange: true,
      refetchOnReconnect: true,
    }
  );

  if (error && typeof error === "object" && "data" in error) {
    const errData = error.data as { message?: string };
    toast(errData.message ?? "Something went wrong.", { id: requestId });
  }

  const [colDefs] = useState<ColDef<DecodedPayrollBatchResponseDef>[]>([
    {
      headerName: "Staff Code",
      field: "payload.staffCode",
    },
    {
      headerName: "Full Name",
      field: "payload.fullName",
    },
    {
      headerName: "Unit",
      field: "payload.unit",
    },
    {
      headerName: "Grade Level",
      field: "payload.gradeLevel",
    },
    {
      headerName: "Gross Pay",
      field: "payload.grossPay",
    },

    {
      headerName: "Pension",
      field: "payload.pension",
    },
    {
      headerName: "PAYE",
      field: "payload.paye",
    },
    {
      headerName: "Total Deduction",
      field: "payload.totalDeduction",
    },
    {
      headerName: "Net Pay",
      field: "payload.netPay",
    },
    {
      headerName: "Failure Reason",
      field: "failureReason",
      valueFormatter: ({ node }) => node?.data?.failureReason || "-",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={
        data?.data
          ? data.data.map((prBtch) => ({
              ...prBtch,
              payload: JSON.parse(prBtch.payload),
            }))
          : []
      }
      setPaginationPageSize={() => {}}
      loading={isLoading}
      hasActionOnRow={false}
    />
  );
};

export default PayrollBatchTable;
