// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { DepartmentResponseDef } from "@/models/response/department";
import { CreateDepartmentDef } from "@/models/validations/department/create-department.validation";
import { UpdateDepartmentDef } from "@/models/validations/department/update-department.validation";
import { baseService } from "./base.service";

export const departmentService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createDepartment: builder.mutation<DefaultResponse, CreateDepartmentDef>({
      query: (credentials) => ({
        url: "/department",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Departments"],
    }),
    updateDepartment: builder.mutation<DefaultResponse, UpdateDepartmentDef>({
      query: (credentials) => ({
        url: "/department/update",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    deleteDepartment: builder.mutation<DefaultResponse, { id: string }>({
      query: (credentials) => ({
        url: "/department/delete",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getDepartment: builder.query<
      DefaultResponse & { data: DepartmentResponseDef[] },
      void
    >({
      query: () => ({
        url: "/department",
        method: "GET",
      }),
      providesTags: ["Departments"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateDepartmentMutation,
  useGetDepartmentQuery,
  useDeleteDepartmentMutation,
  useUpdateDepartmentMutation,
} = departmentService;
