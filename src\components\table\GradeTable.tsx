import { DeleteEntityConstant } from "@/constants/DeleteEntityConstant";
import { ModalConstant } from "@/constants/ModalConstant";
import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { GradeResponseDef } from "@/models/response/grade";
import { useGetGradeQuery } from "@/services/grade.service";
import { modal } from "@/store/module/modal";
import { sheet } from "@/store/module/sheet";
import { ColDef, GridApi, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const GradeTable = () => {
  const { data: response, isLoading } = useGetGradeQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<GradeResponseDef>[]>([
    {
      headerName: "Name",
      field: "name",
    },
    {
      headerName: "Description",
      field: "description",
    },
    {
      headerName: "Created by",
      field: "createdBy",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  const onEditClick = (node: IRowNode<GradeResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.updateGradeSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };

  const onDeleteClick = async ({
    node,
  }: {
    node: IRowNode<GradeResponseDef>;
    api: GridApi;
  }) => {
    if (!node.data) {
      toast.error(`Cadre not found"`);
    }
    dispatch(
      modal.mutation.open({
        open: true,
        modalType: ModalConstant.confirmationModal,
        metadata: {
          id: node.data?.id,
          entity: DeleteEntityConstant.grade,
          title: `Delete "${node.data?.name}" cadre?`,
          description: `Are you sure you want to delete this cadre "${node.data?.name}"`,
          warning: `By deleting this cadre, the associated record will lose access to this cadre.`,
        },
      })
    );
  };

  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={response?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        defaultDeleteAction: onDeleteClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default GradeTable;
