import { AppSidebar } from "@/components/layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarProvider } from "@/components/ui/sidebar";
import { ModalConstant } from "@/constants/ModalConstant";
import { useAppDispatch } from "@/hooks";
import { modal } from "@/store/module/modal";
import { PlusCircleIcon } from "lucide-react";
import React from "react";

export type DashboardLayoutProps = {
  children?: React.ReactNode;
  ExtraActionsBtn?: React.JSX.ElementType;
  heading?: string | React.ReactNode;
  icon?: React.JSX.ElementType;
  addNewEntityBtnLabel?: string;
  addNewEntityModal?: ModalConstant;
};
const DashboardLayout = ({
  children,
  ExtraActionsBtn,
  heading,
  addNewEntityBtnLabel,
  addNewEntityModal,
}: DashboardLayoutProps) => {
  // const { open, isMobile, toggleSidebar } = useSidebar();

  const dispatch = useAppDispatch();

  const onAdd = () => {
    dispatch(
      modal.mutation.open({
        modalType: addNewEntityModal,
      })
    );
  };

  return (
    <>
      <SidebarProvider>
        <AppSidebar />
        <div className="w-full mt-1">
          {heading && (
            <div className="flex items-center justify-between mb-5 border-b px-5 h-16">
              <h1 className="font-medium text-xl">{heading}</h1>
              <div className="flex items-center gap-2">
                {addNewEntityBtnLabel && addNewEntityModal && (
                  <Button onClick={onAdd} size="sm" className="text-xs">
                    <PlusCircleIcon />
                    {addNewEntityBtnLabel}
                  </Button>
                )}
                {ExtraActionsBtn && <ExtraActionsBtn />}
              </div>
            </div>
          )}
          <main className="px-5">{children}</main>
        </div>
      </SidebarProvider>
      )
    </>
  );
};

export default DashboardLayout;
