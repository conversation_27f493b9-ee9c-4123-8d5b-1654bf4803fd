import { Zap } from "lucide-react";
import { Button } from "../ui/button";

const navLinks = [
  {
    title: "Home",
    icon: "/icons/user.svg",
  },
  {
    title: "About",
    icon: "/icons/cart.svg",
  },
  {
    title: "Vendors",
    icon: "/icons/heart.svg",
  },
  {
    title: "Conact Us",
    icon: "/icons/support.svg",
  },
];

const BottomNavbar = () => {
  return (
    <section className="flex items-center justify-between w-full max-lg:hidden">
      <div className="wrapper flex items-center justify-between gap-5">
        <Button>
          <img src="/layout.png" alt="icon" className="text-[0px] w-4 h-4" />
          Browse all Categories
        </Button>

        <div className="flex items-center flex-1 justify-between">
          <Button variant="ghost" size="sm">
            <Zap />
            Hot Deals
          </Button>

          <div>
            {navLinks.map(({ title }) => (
              <Button variant="ghost" size="sm">
                {title}
              </Button>
            ))}
          </div>
        </div>

        <p className="text-sm font-medium">
          Spend ₦30,000 and get free delivery
        </p>
      </div>
    </section>
  );
};

export default BottomNavbar;
