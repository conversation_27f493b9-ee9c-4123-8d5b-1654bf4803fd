import { ModalConstant } from "@/constants/ModalConstant";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export type ModalState = {
  isLoading: boolean;
  modalOptions: {
    open?: boolean;
    modalType?: ModalConstant;
    disableCloseOnBlur?: boolean;
    metadata?: { [key: string]: unknown };
  };
};
const initialState: ModalState = {
  isLoading: false,
  modalOptions: {
    open: false,
    disableCloseOnBlur: false,
    metadata: {},
    modalType: undefined,
  },
};

const actions = {};

const slice = createSlice({
  name: "base",
  initialState,
  reducers: {
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setDisableCloseOnBlur: (state, action: PayloadAction<boolean>) => {
      state.modalOptions = {
        ...state.modalOptions,
        disableCloseOnBlur: action.payload,
      };
    },
    setMetadata: (state, action: PayloadAction<{ [key: string]: unknown }>) => {
      state.modalOptions = {
        ...state.modalOptions,
        metadata: { ...state.modalOptions.metadata, ...action.payload },
      };
    },
    open: (state, action: PayloadAction<ModalState["modalOptions"]>) => {
      console.log(action.payload);

      state.modalOptions = {
        ...state.modalOptions,
        ...action.payload,
        open: true,
      };
    },
    close: (state) => ({
      ...state,
      modalOptions: initialState.modalOptions,
    }),
  },
});

export const modal = {
  reducer: slice.reducer,
  actions: actions,
  mutation: slice.actions,
};
