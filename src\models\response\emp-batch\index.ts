export type EmpBatchResponseDef = {
  createdBy: string;
  approvedBy: string;
  companyId: number;
  createdAt: string;
  updatedAt: string;
  id: string;

  status: string;
  totalCount: number;
  successCount: number;
  failureCount: number;
  failureReason: string | null;
};

export type EmpBatchRecordResponseDef = {
  id: string;
  jobId: string;
  status: string;
  failureReason: string;
  payload: object;
  createdAt: string;
  updatedAt: string;
};
