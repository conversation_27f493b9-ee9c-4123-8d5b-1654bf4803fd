// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { DesignationResponseDef } from "@/models/response/designation";
import { NameDescriptionDef } from "@/models/validations";
import { UpdateDesignationDef } from "@/models/validations/designation/update-designation.validation";
import { baseService } from "./base.service";

export const designationService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createDesignation: builder.mutation<DefaultResponse, NameDescriptionDef>({
      query: (credentials) => ({
        url: "/designation",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Designations"],
    }),
    updateDesignation: builder.mutation<DefaultResponse, UpdateDesignationDef>({
      query: (credentials) => ({
        url: "/designation/update",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Designations"],
    }),
    deleteDesignation: builder.mutation<DefaultResponse, { id: string }>({
      query: (credentials) => ({
        url: "/designation/delete",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Authorization"],
    }),
    getDesignations: builder.query<
      DefaultResponse & { data: DesignationResponseDef[] },
      void
    >({
      query: () => ({
        url: "/designation",
        method: "GET",
      }),
      providesTags: ["Designations"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateDesignationMutation,
  useGetDesignationsQuery,
  useUpdateDesignationMutation,
  useDeleteDesignationMutation,
} = designationService;
