/* eslint-disable @typescript-eslint/no-explicit-any */
import { formatLabel } from "@/lib/utils";

import { ChevronDown, ChevronRight } from "lucide-react";
import { useState } from "react";

const CollapsibleItem = ({ label, value }: { label: string; value: any }) => {
  const [open, setOpen] = useState(false);
  const isComplex = typeof value === "object" && value !== null;
  console.log(value);

  return (
    <div className="border rounded p-3 bg-muted/30 ">
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-2">
          {isComplex && (
            <button onClick={() => setOpen((prev) => !prev)}>
              {open ? (
                <ChevronDown className="w-4 h-4 text-muted-foreground" />
              ) : (
                <ChevronRight className="w-4 h-4 text-muted-foreground" />
              )}
            </button>
          )}

          <p className="text-xs font-medium capitalize">{formatLabel(label)}</p>
        </div>
      </div>

      {open && (
        <div className="pl-5 text-sm text-muted-foreground whitespace-pre-wrap space-y-2">
          {Array.isArray(value) ? (
            value.map((v, i) =>
              typeof v === "object" ? (
                <CollapsibleItem key={i} label={`Item ${i + 1}`} value={v} />
              ) : (
                <div
                  key={i}
                  className="flex justify-between items-center border-b py-1"
                >
                  <span>{String(v)}</span>
                </div>
              )
            )
          ) : typeof value === "object" && value !== null ? (
            Object.entries(value).map(([k, v]) => (
              <CollapsibleItem key={k} label={k} value={v} />
            ))
          ) : (
            <div className="flex justify-between items-center border-b py-1">
              <span>{"String(value)"}</span>
            </div>
          )}
        </div>
      )}
      {typeof value === "string" ? (
        value.startsWith("https") ? (
          <a
            href={value}
            target="_blank"
            className="text-blue-800 text-xs font-medium !underline underline-offset-2"
          >
            Open Link
          </a>
        ) : (
          <p className="text-xs font-medium">{value}</p>
        )
      ) : typeof value === "number" ? (
        <p className="text-xs font-medium">{value}</p>
      ) : (
        ""
      )}
    </div>
  );
};

export default CollapsibleItem;
