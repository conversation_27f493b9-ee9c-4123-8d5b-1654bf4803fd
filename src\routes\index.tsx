import { RouteConstant } from "@/constants/RouteConstant";
import { useAppSelector } from "@/hooks";
import DashboardLayout from "@/view/layout/DashboardLayout";
import {
  Navigate,
  Route,
  Routes,
  useLocation,
  useSearchParams,
} from "react-router-dom";
import { RouteDef, routes } from "./routes";

// Protected Route component that uses Outlet to render children
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { token } = useAppSelector((state) => state.auth);

  if (!token) {
    return <Navigate to={RouteConstant.auth.login} replace />;
  }

  return <>{children}</>; // Render children if authenticated
};

// Guest Route component that handles routing for unauthenticated users
const GuestRoute = ({ children }: { children: React.ReactNode }) => {
  const { token } = useAppSelector((state) => state.auth);
  const [searchParams] = useSearchParams();
  const location = useLocation();

  const currentRoute = routes.find((route) => route.path === location.pathname);

  // Allow access if the route is always public
  if (token && currentRoute?.metadata?.isAlwaysPublic) {
    return <>{children}</>;
  }

  // Redirect authenticated users to the callback URL or dashboard
  const callbackUrl =
    searchParams.get("callbackUrl") || RouteConstant.dashboard.path;

  if (token) {
    if (callbackUrl) {
      return <Navigate to={callbackUrl} replace />;
    }
    return <Navigate to={RouteConstant.dashboard.path} replace />;
  }

  return <>{children}</>;
};

// Main Router Component that sets up all routes
const AppRoutes = () => {
  const { token } = useAppSelector((state) => state.auth);
  const isAuthenticated = !!token;
  const location = useLocation();

  const privateRoutes = routes.filter(
    (route: RouteDef) => route.metadata?.isAuthenticated
  );
  const guestRoutes = routes.filter(
    (route: RouteDef) => !route.metadata?.isAuthenticated
  );

  const renderRouterComponent = (route: RouteDef) => {
    const { Component, metadata } = route;
    return metadata?.hasSidebar ? (
      <DashboardLayout
        heading={metadata.dashboardProps?.heading}
        addNewEntityBtnLabel={metadata.dashboardProps?.addNewEntityBtnLabel}
        addNewEntityModal={metadata.dashboardProps?.addNewEntityModal}
        ExtraActionsBtn={metadata.dashboardProps?.ExtraActionsBtn}
      >
        <Component />
      </DashboardLayout>
    ) : (
      <Component />
    );
  };

  return (
    <Routes>
      {/* Path handler */}

      {/* Guest Route handler */}
      {guestRoutes.map((route, idx) => {
        const { path, metadata } = route;
        if (metadata?.redirectTo) {
          return (
            <Route
              key={`Guest-route-${idx}`}
              path={path}
              element={<Navigate to={metadata.redirectTo} replace />}
            />
          );
        }

        return (
          <Route
            key={`Guest-route-${idx}`}
            path={path}
            element={<GuestRoute>{renderRouterComponent(route)}</GuestRoute>}
            {...metadata}
          />
        );
      })}

      {/* Auth Route Handler */}
      {privateRoutes.map((route, idx) => {
        const { path, metadata } = route;
        if (metadata?.redirectTo) {
          return (
            <Route
              key={`Auth-route-${idx}`}
              path={path}
              element={<Navigate to={metadata.redirectTo} replace />}
            />
          );
        }
        return (
          <Route
            key={`Auth-route-${idx}`}
            path={path}
            element={
              <ProtectedRoute>{renderRouterComponent(route)}</ProtectedRoute>
            }
            {...metadata}
          />
        );
      })}

      {/* Catch All Route handler */}
      <Route
        path="*"
        element={
          <Navigate
            to={
              isAuthenticated
                ? RouteConstant.dashboard.path
                : `${RouteConstant.auth.login}?callbackUrl=${encodeURIComponent(
                    location.pathname
                  )}`
            }
            replace
          />
        }
      />
    </Routes>
  );
};

export const AppRouter = () => {
  return <AppRoutes />;
};
