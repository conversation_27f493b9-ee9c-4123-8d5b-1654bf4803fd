import { Input<PERSON><PERSON>, TextareaField } from "@/components/form-fields";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { ContractTypeResponseDef } from "@/models/response/contract-type";
import {
  UpdateContractTypeDef,
  UpdateContractTypeSchema,
} from "@/models/validations/contract-type/update-contract-type.validation";
import { useUpdateContractTypeMutation } from "@/services/contract-type.service";
import { sheet } from "@/store/module/sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const UpdateContractTypeForm = () => {
  const dispatch = useAppDispatch();

  const [updateContractType, { isLoading: isCreatingRole }] =
    useUpdateContractTypeMutation();

  const {
    sheetOptions: { metadata: sheetMetadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = sheetMetadata as { data: ContractTypeResponseDef };

  const form = useForm<UpdateContractTypeDef>({
    resolver: zodResolver(UpdateContractTypeSchema),
    defaultValues: {
      id: data.id,
      description: data.description,
      name: data.name,
    },
    mode: "all",
  });

  const onSubmit = async (data: UpdateContractTypeDef) => {
    const res = await updateContractType(data).unwrap();

    if (res.success) {
      toast("Request successful and pending authorztion");
      dispatch(sheet.mutation.close());
    }
  };

  return (
    <section className="p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="name"
              label="Name"
              placeholder="Enter Employment Type"
            />
            <TextareaField
              form={form}
              name="description"
              label="Description "
              placeholder="Enter description"
            />
          </div>
          <Button className="w-full font-medium" disabled={isCreatingRole}>
            {isCreatingRole && <LoaderIcon className="animate-spin" />}
            Update Employment Type
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default UpdateContractTypeForm;
