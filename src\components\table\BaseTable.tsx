/* eslint-disable @typescript-eslint/no-explicit-any */
// import { ModalConstant } from "@/constants/ModalConstant";
// import { SheetConstant } from "@/constants/SheetConstant";
// import { formatCurrency } from "@/lib/utils";
import { formattedCurrency } from "@/lib/utils";
import type { ColDef, GridOptions } from "ag-grid-community";
import {
  AllCommunityModule,
  ModuleRegistry,
  themeQuartz,
} from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import { format } from "date-fns";
import { InboxIcon } from "lucide-react";
import { ReactNode, useMemo, useRef, useState } from "react";
import { DefaultHeaderComponentProps } from "./_components/DefaultHeader";
import TablePagination from "./_components/TablePagination";
import ActionCellRenderer, {
  ActionCellProps,
} from "./renderer/ActionCellRenderer ";
import { StatusCellRenderer } from "./renderer/StatusCellRenderer";

ModuleRegistry.registerModules([AllCommunityModule]);

export type SSSROptions = {
  isSsr?: boolean;
  totalCount?: number;
  currentPage?: number;
  action?: (payload?: unknown) => unknown;
  payload?: unknown;
};
export interface BaseTableProps<T> extends GridOptions {
  loading?: boolean;
  tableType?: "noLabel" | "label" | "";
  showTopBar?: boolean;
  // showTopBarOptions?: RedirectHeaderCardProps;
  DefaultHeaderOptions?: DefaultHeaderComponentProps;
  onCellClickAction?: (value?: unknown) => void;
  reloadAction?: (value?: unknown) => void;
  autoReload?: boolean;
  actionOptions?: ActionCellProps;
  customHeaderComponent?: (gridRef?: React.RefObject<AgGridReact>) => ReactNode;
  isPagination?: boolean;
  ssr?: SSSROptions;
  rowData: T[];
  columnDefs: ColDef<T>[];
  paginationPageSize?: number;
  setPaginationPageSize: (pageSize: number) => void;
  paginationPageSizeSelector?: number[];
}

type CustomTableProps<T> = BaseTableProps<T> & {
  onRowSelect?: (row: T) => void;
  hasActionOnRow?: boolean;
  showCustomPagination?: boolean;
  emptyMessage?: string | React.ReactNode;
  tableHeaderProps?: {
    title: string;
    buttonTitle?: string;
    // createSheetComponent?: SheetConstant;
    metadata?: { [key: string]: unknown };
    // modalComponent?: ModalConstant;
    children?: React.ReactNode;
  };
};

const BaseTable = <T,>({
  rowData,
  columnDefs,
  loading,
  onCellClickAction,
  actionOptions,
  hasActionOnRow = true,
  setPaginationPageSize,
  showCustomPagination = true,
  emptyMessage = "",
  ...props
}: CustomTableProps<T>) => {
  const gridRef = useRef<AgGridReact<any> | null>(null);

  const [pageSize] = useState<number>(1000);

  const paginationPageSizeSelector = useMemo(() => {
    return [100, 200, 500, 1000];
  }, []);

  const defaultColDef: BaseTableProps<T>["defaultColDef"] = useMemo(() => {
    return {
      filter: "agTextColumnFilter",
      floatingFilter: false,
      flex: 1,
      minWidth: 100,
      cellStyle: {
        fontSize: "12px",
        // whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
        color: "#0a0d14",
        fontWeight: "500",
      },
    };
  }, []);

  const myTheme = themeQuartz.withParams({
    spacing: 10,
    browserColorScheme: "light",
    fontFamily: "roboto",
    headerFontFamily: "roboto",
    cellFontFamily: "roboto",
    headerFontSize: 12,
    headerFontWeight: 600,
    iconSize: 16,
    rowHeight: 30,
    headerHeight: 35,
  });

  const actionRowData: ColDef = {
    headerName: "Actions",
    field: "actions",
    filter: false,
    suppressNavigable: true,
    maxWidth: 100,
    minWidth: 90,
    sortable: false,
    cellStyle: {
      justification: "center",
      alignItems: "center",
      display: "flex",
      flex: 1,
    },
    pinned: "right",
    cellRenderer: ActionCellRenderer,
    cellRendererParams: {
      actionOptions: actionOptions,
    },
  };

  const styledColumnDefs = useMemo(() => {
    return columnDefs?.map((col) => ({
      ...col,
      cellRenderer: col.field?.toLowerCase().endsWith("status")
        ? StatusCellRenderer
        : col.cellRenderer, // Keep existing renderers if any
      minWidth: col.field?.toLowerCase().endsWith("status")
        ? 100
        : col.minWidth,
      maxWidth: col.field?.toLowerCase().endsWith("status")
        ? 120
        : col.maxWidth,
      width: col.field?.toLowerCase().endsWith("status") ? 100 : col.width,
      valueFormatter: col.field?.endsWith("CreatedAt")
        ? ({ data }: { data?: Record<string, string | number | Date> }) => {
            const rawValue = data?.[col.field as string];
            const date = new Date(rawValue as string);
            return rawValue && !isNaN(date.getTime())
              ? format(date, "PP pp")
              : "";
          }
        : col.headerName?.toLowerCase().endsWith("amount") ||
          col.headerName?.toLowerCase().endsWith("balance") ||
          col.headerName?.toLowerCase().endsWith("salary") ||
          col.headerName?.toLowerCase().endsWith("deductions") ||
          col.headerName?.toLowerCase().endsWith("allowances") ||
          col.headerName?.toLowerCase().endsWith("pay") ||
          col.headerName?.toLowerCase().endsWith("pension") ||
          col.headerName?.toLowerCase().endsWith("tax") ||
          col.headerName?.toLowerCase().endsWith("allowance") ||
          col.headerName?.toLowerCase().endsWith("paye")
        ? ({ data }: { data?: Record<string, string | number> }) => {
            const val = data?.[col.field as string];
            return typeof val === "number"
              ? formattedCurrency(val)
              : val?.toString() ?? ""; // <- Ensure string return
          }
        : col.valueFormatter,
    }));
  }, [columnDefs]);

  return (
    <div className={"w-full max-h-full  min-h-fit  !text-xs"}>
      {rowData?.length > 0 ? (
        <>
          <AgGridReact
            rowData={rowData}
            columnDefs={[
              ...styledColumnDefs,
              ...(hasActionOnRow ? [actionRowData] : []),
            ]}
            className={"w-full min-h-fit max-h-full"}
            domLayout={"autoHeight"}
            rowGroupPanelShow={"always"}
            defaultColDef={defaultColDef}
            autoSizePadding={0}
            loading={loading}
            ensureDomOrder={true}
            enableCellTextSelection={true}
            selectionColumnDef={{
              pinned: "left",
            }}
            pagination={true}
            rowSelection={undefined}
            suppressPaginationPanel
            theme={myTheme}
            paginateChildRows={true}
            serverSideInitialRowCount={10}
            rowModelType="clientSide"
            paginationPageSize={Number(pageSize)}
            paginationPageSizeSelector={paginationPageSizeSelector}
            onCellClicked={(event) => {
              if (event.colDef.headerName?.toLowerCase() == "action") {
                event.event?.stopPropagation();
              } else {
                if (onCellClickAction) {
                  onCellClickAction(event.data);
                }
              }
            }}
            ref={gridRef}
            {...props}
          />
          {showCustomPagination && (
            <TablePagination
              gridRef={gridRef}
              isSsr={!!props.ssr?.isSsr}
              totalPages={4000}
              currentPage={props.ssr?.currentPage}
              paginationPageSizeSelector={paginationPageSizeSelector}
              setPaginationPageSize={setPaginationPageSize}
            />
          )}
        </>
      ) : typeof emptyMessage !== "string" ? (
        <div>{emptyMessage}</div>
      ) : (
        <div className="h-full flex flex-col items-center justify-center bg-gray-100 rounded-md p-6 space-y-2">
          <InboxIcon className="w-10 h-10 text-gray-400" />
          <p className="text-sm text-gray-500">
            {emptyMessage.trim() !== "" ? emptyMessage : "No entity data"}
          </p>
        </div>
      )}
    </div>
  );
};

export default BaseTable;
