import { CreateEmployeeDef } from "@/models/validations/employee/create-employee.validation";

export const EMPLOYEE_FIELD_MAPPING_CONSTANT: {
  label: string;
  name: keyof CreateEmployeeDef;
}[] = [
  { label: "Title", name: "title" },
  { label: "Grade *", name: "jobGradeName" },
  { label: "Grade Level *", name: "gradeLevelName" },
  { label: "Department *", name: "departmentName" },
  { label: "Unit *", name: "unitName" },
  { label: "Branch *", name: "branchName" },
  { label: "Job Title *", name: "jobTitleName" },
  { label: "Salary Package *", name: "salaryPackageName" },
  { label: "BVN *", name: "bvn" },
  { label: "NIN *", name: "nin" },

  { label: "Email *", name: "email" },
  { label: "Phone 1 *", name: "phone1" },

  { label: "First Name *", name: "firstName" },
  { label: "Last Name *", name: "lastName" },
  { label: "Middle Name", name: "middleName" },
  { label: "Staff Code *", name: "staffCode" },
  { label: "Birthday", name: "birthday" },
  { label: "Employment Type", name: "contractTypeName" },

  { label: "Gender *", name: "gender" },
  { label: "Marital Status", name: "maritalStatus" },
  { label: "Nationality", name: "nationality" },

  { label: "State of Origin", name: "stateOfOrigin" },
  { label: "Local Govt", name: "localGovt" },

  { label: "Residential Address *", name: "residentialAddress" },
  { label: "Residential Local Govt", name: "residentialLocalGovt" },
  { label: "Residential State", name: "residentialState" },
  { label: "Residential Country", name: "residentialCountry" },

  { label: "Phone 2", name: "phone2" },

  { label: "Place of Birth", name: "placeOfBirth" },
  { label: "Religion", name: "religion" },

  { label: "Next of Kin Full Name", name: "nextOfKinFullName" },
  { label: "Next of Kin Relationship", name: "nextOfKinRelationship" },
  { label: "Next of Kin Phone Number", name: "nextOfKinPhoneNumber" },
  { label: "Next of Kin Email", name: "nextOfKinEmail" },
  { label: "Next of Kin Address", name: "nextOfKinAddress" },

  { label: "Highest Qualification", name: "highestQualification" },
  { label: "Course", name: "course" },
  { label: "Institution Name", name: "institutionName" },
  { label: "Date of Graduation", name: "dateOfGraduation" },

  { label: "Date Employed", name: "dateEmployed" },

  { label: "Name of Spouse", name: "nameOfSpouse" },
  { label: "No. of Children", name: "noOfChildren" },

  { label: "Tax ID", name: "taxId" },
  { label: "Pension ID", name: "pensionId" },
  { label: "PFA", name: "pfa" },

  { label: "Date Appointed To Level", name: "dateAppointedToLevel" },

  { label: "Account Number", name: "accountNumber" },
  { label: "Passport", name: "passport" },
  { label: "Certificate", name: "certificate" },
  { label: "Guarantor Passport", name: "guarantorPassport" },
  { label: "Guarantor Full Name", name: "guarantorFullname" },
  { label: "Guarantor Phone Number", name: "guarantorPhoneNumber" },
  { label: "Guarantor Relationship", name: "guarantorRelationShip" },
  { label: "Guarantor Address", name: "guarantorAddress" },
  { label: "Guarantor Occupation", name: "guarantorOccupation" },
  {
    label: "Guarantor Means of Identification",
    name: "guarantorMeansOfIdentification",
  },
];
