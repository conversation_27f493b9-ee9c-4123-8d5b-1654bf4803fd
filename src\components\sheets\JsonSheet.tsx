"use client";
import { useAppSelector } from "@/hooks";
import { isValidJSON } from "@/lib/utils";
import React<PERSON><PERSON> from "react-json-view"; // use the component in your
import { SheetHeader, SheetTitle } from "../ui/sheet";

const JsonSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  return (
    <>
      <SheetHeader>
        <SheetTitle className=" font-semibold text-lg">
          {metadata?.title as string}
        </SheetTitle>
      </SheetHeader>
      <div className="mt-5 overflow-hidden">
        {metadata?.data ? (
          isValidJSON(metadata?.data as string) && metadata.data !== "null" ? (
            <ReactJson src={JSON.parse(metadata.data as string)} />
          ) : (
            <p>{metadata?.data as string}</p>
          )
        ) : (
          <p>No data</p>
        )}
      </div>
    </>
  );
};

export default JsonSheet;
