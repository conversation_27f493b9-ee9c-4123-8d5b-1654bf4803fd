// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { LocationResponseDef } from "@/models/response/locations";
import { baseService } from "./base.service";

export const locationService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    getLocations: builder.query<
      DefaultResponse & { data: LocationResponseDef[] },
      void
    >({
      query: () => ({
        url: "/location/read",
        method: "GET",
      }),
      providesTags: ["Locations"],
    }),
  }),
  overrideExisting: false,
});

export const { useGetLocationsQuery } = locationService;
