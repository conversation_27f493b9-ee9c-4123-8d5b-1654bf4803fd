import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { EmpBatchResponseDef } from "@/models/response/emp-batch";
import { useGetEmpBatchQuery } from "@/services/employee-batch.service";
import { sheet } from "@/store/module/sheet";
import { ColDef } from "ag-grid-community";
import { format } from "date-fns";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import BaseTable from "./BaseTable";

const BatchTable = () => {
  const { data: response, isLoading } = useGetEmpBatchQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<EmpBatchResponseDef>[]>([
    {
      headerName: "Total records",
      field: "totalCount",
    },
    {
      headerName: "Success count",
      field: "successCount",
    },
    {
      headerName: "Failure count",
      field: "failureCount",
    },
    {
      headerName: "Created by",
      field: "createdBy",
    },
    {
      headerName: "Approved by",
      field: "approvedBy",
    },
    {
      headerName: "Created at",
      field: "createdAt",
      valueGetter: ({ node }) =>
        node?.data
          ? format(node?.data.createdAt, "PPPp")
          : node?.data?.createdAt,
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={response?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        showDefault: true,
        actions: [
          {
            title: "View Records",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.batchRecordSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default BatchTable;
