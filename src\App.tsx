import { BrowserRouter } from "react-router-dom";
import ModalProvider from "./components/providers/ModalProvider";
import SheetProvider from "./components/providers/SheetProvider";
import { Toaster } from "./components/ui/sonner";
import { AppRouter } from "./routes";
const App = () => {
  return (
    <BrowserRouter>
      <AppRouter />
      <Toaster position="top-right" />
      <ModalProvider />
      <SheetProvider />
    </BrowserRouter>
  );
};

export default App;
