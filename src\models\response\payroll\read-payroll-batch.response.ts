export type PayrollBatchResponseDef = {
  id: string;
  payrollUploadId: string;
  status: string;
  failureReason: string;
  createdAt: string;

  payload: string;
};

export type DecodedPayrollBatchResponseDef = {
  id: string;
  payrollUploadId: string;
  status: string;
  failureReason: string;
  createdAt: string;

  payload: {
    staffCode: string;
    fullName: string;
    unit: string;
    gradeLevel: string;
    grossPay: number;
    daysWorkedPreviousPayroll: number;
    daysWorkedCurrentPayroll: number;
    apprenticeAllowance: number;
    specialCategoryAllowance: number;
    lunchSubsidyAllowance: number;
    monthlyBasicSalary: number;
    housingAllowance: number;
    transportAllowance: number;
    utilityAllowance: number;
    selfMaintenance: number;
    furnitureAllowance: number;
    hazardAllowance: number;
    levelProficiency: number;
    fuelSubsidy: number;
    childEducationSubsidy: number;
    domesticStaff: number;
    responsibility: number;
    managementFee: number;
    grossPayAlt: number;
    amortisedGross: number;
    vehicleAmortisation: number;
    leaveAllowance: number;
    performanceBonus: number;
    inconvenienceAllowance: number;
    overTime: number;
    outstandingSalary: number;
    iouRecovery: number;
    loanSalaryAdvanceDeduction: number;
    productCashShortage: number;
    lateness: number;
    absenteeism: number;
    otherPenalty: number;
    otherDeduction: number;
    cooperativeContribution: number;
    pension: number;
    paye: number;
    grossPayable: number;
    amortisedPaye: number;
    totalDeduction: number;
    netPay: number;
    annualGross: number;
    annualPension: number;
    otherReliefs: number;
    consolidatedRelief: number;
    taxableIncome: number;
    monthlyTax: number;
  };
};
