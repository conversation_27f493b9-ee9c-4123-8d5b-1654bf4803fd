// services/authApi.ts

import { VerifyBvnRequestDef } from "@/models/request/kyc/verify-bvn.request";
import { VerifyNinRequestDef } from "@/models/request/kyc/verify-nin.request";
import { DefaultResponse } from "@/models/response/default.response";
import { VerifyBvnResponseDef } from "@/models/response/kyc/verify-bvn.response";
import { VerifyNinResponseDef } from "@/models/response/kyc/verify-nin.response";
import { baseService } from "./base.service";

export const kycService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    verifyBvn: builder.mutation<
      DefaultResponse & { data: VerifyBvnResponseDef },
      VerifyBvnRequestDef
    >({
      query: (credentials) => ({
        url: "/kyc/verify/bvn",
        method: "POST",
        body: credentials,
      }),
    }),
    verifyNin: builder.mutation<
      DefaultResponse & { data: VerifyNinResponseDef },
      VerifyNinRequestDef
    >({
      query: (credentials) => ({
        url: "/kyc/verify/nin",
        method: "POST",
        body: credentials,
      }),
    }),
  }),
  overrideExisting: false,
});

export const { useVerifyBvnMutation, useVerifyNinMutation } = kycService;
