"use client";

import { useAppSelector } from "@/hooks";
import { ContractTypeResponseDef } from "@/models/response/contract-type";
import UpdateContractTypeForm from "../forms/update/UpdateContractTypeForm";
import { DialogDescription } from "../ui/dialog";
import { SheetHeader, SheetTitle } from "../ui/sheet";

const UpdateContractTypeSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: ContractTypeResponseDef };
  return (
    <>
      <SheetHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <SheetTitle className="">Update Employment Type</SheetTitle>
        <DialogDescription className="max-w-sm">{`Update "${data.name}" Employment Type`}</DialogDescription>
      </SheetHeader>
      <UpdateContractTypeForm />
    </>
  );
};

export default UpdateContractTypeSheet;
