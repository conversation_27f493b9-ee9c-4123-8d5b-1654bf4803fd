"use client";

import { useAppSelector } from "@/hooks";
import { RoleResponseDef } from "@/models/response/role";
import UpdateRoleForm from "../forms/update/UpdateRoleForm";
import { DialogDescription } from "../ui/dialog";
import { She<PERSON>Header, SheetTitle } from "../ui/sheet";

const UpdateRoleSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: RoleResponseDef };
  return (
    <>
      <SheetHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <SheetTitle className="">Update role</SheetTitle>
        <DialogDescription className="max-w-sm">{`Update "${data.name}" role`}</DialogDescription>
      </SheetHeader>
      <UpdateRoleForm />
    </>
  );
};

export default UpdateRoleSheet;
