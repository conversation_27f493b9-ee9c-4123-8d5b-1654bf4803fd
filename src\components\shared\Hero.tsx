import { Button } from "../ui/button";

const Hero = () => {
  return (
    <section className="wrapper">
      <section className="w-full : flex justify-between rounded-lg h-[296px] relative bg-[#807AF9] px-10 overflow-clip after:content-[''] after:absolute after:inset-0 after:bg-black/10 after:rounded-lg after:z-0">
        <div className="max-w-xl space-y-4 h-full flex flex-col justify-center relative z-50">
          <p className="font-bold text-4xl lg:text-5xl text-white">
            Enjoy in app discounts on products
          </p>
          <p className="text-base">Up to 30% off</p>
          <Button className="grow-0 w-max z-50 cursor-pointer">Shop Now</Button>
        </div>
        <img
          alt="hero"
          src="/hero.png"
          className="w-[484px]  sm:h-full sm:-bottom-5 object-contain object-right absolute max-sm:-bottom-10 right-0"
        />
        {/* <div className="absolute bg-black/15 w-full h-full left-0 z-10" /> */}
      </section>
    </section>
  );
};

export default Hero;
