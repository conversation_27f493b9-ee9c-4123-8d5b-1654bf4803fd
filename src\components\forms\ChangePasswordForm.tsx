import { useAppDispatch, useAppSelector } from "@/hooks";
import {
  ChangePasswordDef,
  ChangePasswordSchema,
} from "@/models/validations/auth/change-password.validation";
import { useChangePasswordMutation } from "@/services/auth.service";
import { modal } from "@/store/module/modal";
import { resetState } from "@/store/module";
import { RouteConstant } from "@/constants/RouteConstant";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { InputField } from "../form-fields";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const ChangePasswordForm = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { data: companyData } = useAppSelector((state) => state.company);
  const [changePassword, { isLoading }] = useChangePasswordMutation();

  const form = useForm<ChangePasswordDef>({
    resolver: zodResolver(ChangePasswordSchema),
    defaultValues: {
      // username: account?.userEmail,
    },
    mode: "all",
  });

  // Reset email and password when the user type changes

  const onSubmit = async (data: ChangePasswordDef) => {
    const { oldPassword, newPassword } = data;
    const res = await changePassword({
      oldPassword,
      newPassword,
    }).unwrap();

    if (res.success) {
      toast.success("Password changed successfully. Please login again with your new password.");
      dispatch(modal.mutation.close());
      
      // Reset all application state to clear user data
      dispatch(resetState());
      
      // Get company ID for redirect
      const companyId = companyData?.id || localStorage.getItem("cid");
      const loginUrl = companyId 
        ? `${RouteConstant.auth.login}?cid=${companyId}`
        : RouteConstant.auth.login;
      
      // Redirect to login page after a short delay to show the toast
      setTimeout(() => {
        navigate(loginUrl);
      }, 1000);
    }
  };

  return (
    <section className="max-w-md mx-auto place-content-center sm:min-w-lg">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4  p-4">
          <div className="space-y-4">
            <InputField
              form={form}
              name="oldPassword"
              label="Old Password"
              type="password"
              placeholder="Enter your current password"
            />
            <InputField
              form={form}
              name="newPassword"
              label="New Password"
              type="password"
              placeholder="••••••••"
            />
            <InputField
              form={form}
              name="confirmPassword"
              label="Confirm New Password"
              type="password"
              placeholder="••••••••"
            />
          </div>

          <Button
            className="w-full font-medium !bg-primary"
            variant="default"
            disabled={isLoading}
          >
            {isLoading && <LoaderIcon className="animate-spin" />}
            Change Password
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default ChangePasswordForm;
