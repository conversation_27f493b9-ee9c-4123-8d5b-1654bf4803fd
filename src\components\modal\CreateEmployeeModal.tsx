"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateEmployeeForm from "../forms/CreateEmployeeForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const CreateEmployeeModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new Employee</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateEmployeeForm />
    </>
  );
};

export default CreateEmployeeModal;
