"use client";

import { DialogTitle } from "@radix-ui/react-dialog";
import CreateSalaryPackageForm from "../forms/CreateSalaryPackageForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const SalaryPackageModal = () => {
  return (
    <div className="md:w-full md:min-w-2xl">
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="">Create new salary package</DialogTitle>
        <DialogDescription className="max-w-sm"></DialogDescription>
      </DialogHeader>
      <CreateSalaryPackageForm />
    </div>
  );
};

export default SalaryPackageModal;
