// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { UnitResponseDef } from "@/models/response/unit";
import { CreateUnitDef } from "@/models/validations/unit/create-unit.validation";
import { baseService } from "./base.service";

export const unitService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createUnit: builder.mutation<DefaultResponse, CreateUnitDef>({
      query: (credentials) => ({
        url: "/unit/create",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Units"],
    }),
    getUnits: builder.query<
      DefaultResponse & { data: UnitResponseDef[] },
      void
    >({
      query: () => ({
        url: "/unit",
        method: "GET",
      }),
      providesTags: ["Units"],
    }),
  }),
  overrideExisting: false,
});

export const { useCreateUnitMutation, useGetUnitsQuery } = unitService;
