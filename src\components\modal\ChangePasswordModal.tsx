import { DialogTitle } from "@radix-ui/react-dialog";
import ChangePasswordForm from "../forms/ChangePasswordForm";
import { DialogDescription, DialogHeader } from "../ui/dialog";

const ChangePasswordModal = () => {
  return (
    <>
      <DialogHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <DialogTitle className="font-medium">Change Password</DialogTitle>
        <DialogDescription className="max-w-sm">
          {/* {data?.roleName} */}
        </DialogDescription>
      </DialogHeader>
      <ChangePasswordForm />
    </>
  );
};

export default ChangePasswordModal;
