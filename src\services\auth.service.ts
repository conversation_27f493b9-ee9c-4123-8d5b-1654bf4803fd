// services/authApi.ts

import { ChangePasswordRequestDef } from "@/models/request/auth/change-password.request";
import { CompleteForgotPasswordRequestDef } from "@/models/request/auth/complete-forgot-password.request";
import { InitiateForgotPasswordRequestDef } from "@/models/request/auth/initiate-forgot-password.request";
import { LoginRequestDef } from "@/models/request/auth/login.request";
import { LoginResponseDef } from "@/models/response/auth/login.response";
import { DefaultResponse } from "@/models/response/default.response";
import { baseService } from "./base.service";

export const authApi = baseService.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation<
      DefaultResponse & { data: LoginResponseDef },
      LoginRequestDef
    >({
      query: (credentials) => ({
        url: "/auth/login",
        method: "POST",
        body: credentials,
      }),
    }),
    resendAuthOtp: builder.mutation<DefaultResponse, { otpRef: string }>({
      query: (credentials) => ({
        url: "/auth/resend-otp",
        method: "POST",
        body: credentials,
      }),
    }),
    initiateForgotPassword: builder.mutation<
      DefaultResponse & { data: { otpRef: string } },
      InitiateForgotPasswordRequestDef
    >({
      query: (credentials) => ({
        url: "/auth/initiate/forgot-password",
        method: "POST",
        body: credentials,
      }),
    }),
    completeForgotPassword: builder.mutation<
      DefaultResponse,
      CompleteForgotPasswordRequestDef
    >({
      query: (credentials) => ({
        url: "/auth/complete/forgot-password",
        method: "POST",
        body: credentials,
      }),
    }),
    switchCompany: builder.mutation<
      DefaultResponse & { data: LoginResponseDef },
      { companyId: string }
    >({
      query: (credentials) => ({
        url: "/auth/switch-comapny",
        method: "POST",
        body: credentials,
      }),
    }),
    changePassword: builder.mutation<DefaultResponse, ChangePasswordRequestDef>(
      {
        query: (credentials) => ({
          url: "/auth/change-password",
          method: "POST",
          body: credentials,
        }),
      }
    ),
  }),
  overrideExisting: false,
});

export const {
  useLoginMutation,
  useSwitchCompanyMutation,
  useInitiateForgotPasswordMutation,
  useCompleteForgotPasswordMutation,
  useResendAuthOtpMutation,
  useChangePasswordMutation,
} = authApi;
