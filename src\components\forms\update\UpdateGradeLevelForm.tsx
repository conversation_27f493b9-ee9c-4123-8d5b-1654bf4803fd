import { Input<PERSON>ield, TextareaField } from "@/components/form-fields";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { GradeLevelResponseDef } from "@/models/response/grade-level";
import {
  UpdateGradeLevelDef,
  UpdateGradeLevelSchema,
} from "@/models/validations/grade-level/update-grade-level.validation";
import { useUpdateGradeLevelMutation } from "@/services/employee-group.service";
import { sheet } from "@/store/module/sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const UpdateGradeLevelForm = () => {
  const dispatch = useAppDispatch();

  const [updateGradeLevel, { isLoading: isUpdatingGradeLevel, error }] =
    useUpdateGradeLevelMutation();

  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: GradeLevelResponseDef };

  const form = useForm<UpdateGradeLevelDef>({
    resolver: zodResolver(UpdateGradeLevelSchema),
    defaultValues: {
      id: data.id,
      description: data.description,
      name: data.name,
    },
    mode: "all",
  });

  const onSubmit = async (data: UpdateGradeLevelDef) => {
    try {
      const res = await updateGradeLevel({
        ...data,
        description: data.description || data.name,
      }).unwrap();

      if (res.success) {
        toast("Grade level request submitted and is pending authorization");
        dispatch(sheet.mutation.close());
      }
    } catch {
      console.log(error);
    }
  };

  console.log(form.formState.errors);

  return (
    <section className="w-full p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <>
              <InputField form={form} name="name" label="Grade level name" />

              <TextareaField
                form={form}
                name="description"
                label="Description"
              />

              <div className="flex gap-4 justify-between">
                <Button className="font-medium" disabled={isUpdatingGradeLevel}>
                  {isUpdatingGradeLevel && (
                    <LoaderIcon className="animate-spin" />
                  )}
                  Update new grade level
                </Button>
              </div>
            </>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default UpdateGradeLevelForm;
