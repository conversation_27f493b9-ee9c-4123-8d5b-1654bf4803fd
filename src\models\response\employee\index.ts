export type EmployeeResponseDef = {
  id: string;
  title: string;
  staffCode: string;
  firstName: string;
  lastName: string;
  middleName: string;
  birthday: string;
  placeOfBirth: string;
  religion: string;
  taxId: string;
  pensionId: string;
  pfa: string;
  dateAppointedToLevel: string;
  gender: "Male" | "Female" | string;
  maritalStatus: string;
  nationality: string;
  stateOfOrigin: string;
  localGovt: string;
  residentialAddress: string;
  residentialLocalGovt: string;
  residentialState: string;
  residentialCountry: string;
  phone1: string;
  phone2: string;
  email: string;
  nextOfKinFullName: string;
  nextOfKinRelationship: string;
  nextOfKinPhoneNumber: string;
  nextOfKinEmail: string;
  nextOfKinAddress: string;
  highestQualification: string;
  course: string;
  institutionName: string;
  institutionAddress: string | null;
  dateOfGraduation: string;
  dateEmployed: string;
  bvn: string;
  nin: string;
  kycVerified: boolean;
  nameOfSpouse: string;
  noOfChildren: string;
  bankName: string;
  bankSortCode: string | null;
  accountNumber: string | null;
  accountName: string | null;
  accountStatus: "PENDING" | "ACTIVE" | string;
  passport: string | null;
  certificate: string | null;
  guarantorPassport: string | null;
  guarantorFullname: string;
  guarantorPhoneNumber: string;
  guarantorRelationShip: string;
  guarantorAddress: string;
  guarantorOccupation: string;
  guarantorMeansOfIdentification: string | null;
  companyId: string;
  grossSalary: number | null;
  tax: number | null;
  otherDeductions: number | null;
  currency: string | null;
  status: "ACTIVE" | "INACTIVE" | string;
  createdAt: string;
  updatedAt: string;
  gradeLevelName: string | null;
  branchName: string;
  jobTitleId: string;
  unitName: string | null;
  createdBy: string;
  approvedBy: string;
  jobGradeId: number | null;
  contractTypeName: string | null;

  jobTitleName: string;
  jobGradeName: string;
  jobClusterName: string | null;
  departmentName: string;
  salaryPackageName: string;
};
