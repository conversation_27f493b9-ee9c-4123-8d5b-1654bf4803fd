export type UploadCsvOptions = {
  file: File;
  cloudName: string;
  uploadPreset: string;
  onError?: (errors: string[]) => void;
};

/**
 * Uploads a CSV file to Cloudinary and returns the secure URL.
 */
export async function uploadFileToCloudinary({
  file,
  cloudName,
  uploadPreset,
  onError,
}: UploadCsvOptions): Promise<string | undefined> {
  try {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("upload_preset", uploadPreset);
    formData.append("resource_type", "raw");

    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${cloudName}/raw/upload`,
      {
        method: "POST",
        body: formData,
      }
    );

    const result = await response.json();

    if (!response.ok || !result.secure_url) {
      onError?.([result.error?.message || "❌ Failed to upload to Cloudinary"]);
      return;
    }

    return result.secure_url as string;
  } catch (err) {
    console.error(err);
    onError?.(["❌ Something went wrong while uploading the CSV"]);
  }
}
