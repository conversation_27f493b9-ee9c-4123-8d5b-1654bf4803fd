import { formattedCurrency } from "@/lib/utils";
import { Link } from "react-router-dom";
import Rating from "../shared/Rating";
import { Button } from "../ui/button";

type ProductCardProps = {
  product: { name: string; image: string; price: number; rating: number };
};
const ProductCard = ({
  product: { image, name, price, rating },
}: ProductCardProps) => {
  return (
    <div className="border w-[181px] h-[286px] bg-white">
      <Link to={"/"}>
        <img
          src={image}
          alt="Bread"
          className="w-full h-[131px] object-contain bg-white"
        />
        <div className="px-3 flex flex-col mt-4 space-y-1">
          <p className="text-black font-medium text-sm line-clamp-2 h-10">
            {name}
          </p>
          <Rating rating={rating} />
          <p className="text-primary font-bold text-sm">
            {formattedCurrency(price)}
          </p>
        </div>
      </Link>
      <div className="px-3">
        <Button className="mt-2 w-full">Add to cart</Button>
      </div>
    </div>
  );
};

export default ProductCard;
