/* eslint-disable @typescript-eslint/no-explicit-any */
import { Dialog, DialogContent } from "@/components/ui/dialog";
import getCroppedImg from "@/lib/getCroppedImg";
import { useCallback, useState } from "react";
import <PERSON><PERSON><PERSON> from "react-easy-crop";

interface CropModalProps {
  open: boolean;
  image: string;
  onClose: () => void;
  onCropDone: (croppedBlob: Blob) => void;
}

export default function CropModal({
  open,
  image,
  onClose,
  onCropDone,
}: CropModalProps) {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<any>(null);

  const onCropComplete = useCallback((_: any, area: any) => {
    setCroppedAreaPixels(area);
  }, []);

  const handleCrop = async () => {
    const croppedBlob = await getCroppedImg(image, croppedAreaPixels);
    onCropDone(croppedBlob);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[90vw] max-h-[90vh] p-4 flex flex-col gap-4">
        <div className="relative w-full h-[400px] bg-muted">
          <Cropper
            image={image}
            crop={crop}
            zoom={zoom}
            aspect={1 / 1}
            onCropChange={setCrop}
            onZoomChange={setZoom}
            onCropComplete={onCropComplete}
          />
        </div>
        <div className="flex justify-end gap-2">
          <button
            onClick={handleCrop}
            className="bg-black text-white px-4 py-2 rounded-md"
          >
            Crop
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
