import { useAppSelector } from "@/hooks";
import { ReadPayrollRecordResponse } from "@/models/response/payroll/read-payroll-record.response";
import { PayrollUploadResponseDef } from "@/models/response/payroll/read-payroll.response";
import { useGetPayrollRecordsQuery } from "@/services/payroll.service";
import { skipToken } from "@reduxjs/toolkit/query";
import type { ColDef, ValueFormatterParams } from "ag-grid-community";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const PayrollRecordTable = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data: selectedPayroll } = metadata as {
    data?: PayrollUploadResponseDef;
  };

  const { data, isLoading, error, requestId } = useGetPayrollRecordsQuery(
    selectedPayroll?.id ? { payrollUploadId: selectedPayroll.id } : skipToken,
    {
      refetchOnMountOrArgChange: true,
      refetchOnReconnect: true,
    }
  );

  if (error && typeof error === "object" && "data" in error) {
    const errData = error.data as { message?: string };
    toast(errData.message ?? "Something went wrong.", { id: requestId });
  }

  const [colDefs] = useState<ColDef<ReadPayrollRecordResponse>[]>(() => {
    const sample = {
      staffCode: "",
      fullName: "",
      unit: "",
      gradeLevel: "",
      grossPay: 0,
      daysWorkedPreviousPayroll: 0,
      accountNumber: "",
      daysWorkedCurrentPayroll: 0,
      apprenticeAllowance: 0,
      specialCategoryAllowance: 0,
      lunchSubsidyAllowance: 0,
      monthlyBasicSalary: 0,
      housingAllowance: 0,
      transportAllowance: 0,
      utilityAllowance: 0,
      selfMaintenance: 0,
      furnitureAllowance: 0,
      hazardAllowance: 0,
      levelProficiency: 0,
      fuelSubsidy: 0,
      childEducationSubsidy: 0,
      domesticStaff: 0,
      responsibility: 0,
      managementFee: 0,
      grossPayAlt: 0,
      amortisedGross: 0,
      vehicleAmortisation: 0,
      leaveAllowance: 0,
      performanceBonus: 0,
      inconvenienceAllowance: 0,
      overTime: 0,
      outstandingSalary: 0,
      iouRecovery: 0,
      loanSalaryAdvanceDeduction: 0,
      productCashShortage: 0,
      lateness: 0,
      absenteeism: 0,
      otherPenalty: 0,
      otherDeduction: 0,
      cooperativeContribution: 0,
      pension: 0,
      paye: 0,
      grossPayable: 0,
      amortisedPaye: 0,
      totalDeduction: 0,
      netPay: 0,
      annualGross: 0,
      annualPension: 0,
      otherReliefs: 0,
      consolidatedRelief: 0,
      taxableIncome: 0,
      monthlyTax: 0,
      createdAt: "",
    };

    return (Object.keys(sample) as (keyof ReadPayrollRecordResponse)[]).map(
      (key) => ({
        field: key,
        headerName: key
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (s) => s.toUpperCase()),
        sortable: true,
        filter: true,
        valueFormatter:
          key === "createdAt"
            ? ({ value }: ValueFormatterParams<ReadPayrollRecordResponse>) =>
                value ? new Date(value).toLocaleDateString() : "-"
            : undefined,
      })
    );
  });

  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={data?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      hasActionOnRow={false}
    />
  );
};

export default PayrollRecordTable;
