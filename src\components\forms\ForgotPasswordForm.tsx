import { RouteConstant } from "@/constants/RouteConstant";
import { useAppSelector } from "@/hooks";
import {
  ForgotPasswordDef,
  ForgotPasswordSchema,
} from "@/models/validations/auth/forgot-password.validation";
import {
  useCompleteForgotPasswordMutation,
  useInitiateForgotPasswordMutation,
  useResendAuthOtpMutation,
} from "@/services/auth.service";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { FieldPath, useForm } from "react-hook-form";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "sonner";
import { CheckBoxField, InputField, OtpInputField } from "../form-fields";
import AuthFormHeader from "../sections/AuthFormHeader";
import { But<PERSON> } from "../ui/button";
import { Form } from "../ui/form";

const headers = [
  {
    title: "Forgot your password?",
    description: "Enter your account email to receive a verification code.",
  },
  {
    title: "Reset your password",
    description: "Enter the code sent to your email and set a new password.",
  },
];

const ForgotPasswordForm = () => {
  const navigate = useNavigate();

  const { data: accountData } = useAppSelector((state) => state.account);
  const { data } = useAppSelector((state) => state.company);
  const [cooldown, setCooldown] = useState(0);

  const [otpRef, setOtpRef] = useState<string>("");

  const [searchParams] = useSearchParams();

  const cid = searchParams.get("cid");

  console.log(accountData);

  const [initiateForgotPassword, { isLoading }] =
    useInitiateForgotPasswordMutation();

  const [completeForgotPassword, { isLoading: isCompletingForgotPassword }] =
    useCompleteForgotPasswordMutation();

  const [resendAuthOtp, { isLoading: isResendingAuthOtp }] =
    useResendAuthOtpMutation();

  const [currentStep, setCurrentStep] = useState(0);

  const form = useForm<ForgotPasswordDef>({
    resolver: zodResolver(ForgotPasswordSchema),
    defaultValues: {},
    mode: "all",
  });

  useEffect(() => {
    if (cooldown <= 0) return;
    const timer = setInterval(() => {
      setCooldown((prev) => prev - 1);
    }, 1000);
    return () => clearInterval(timer);
  }, [cooldown]);

  const onSubmit = async (data: ForgotPasswordDef) => {
    console.log(data);
  };

  //  Handle step change
  const validateSelectedField = async (
    fields: FieldPath<ForgotPasswordDef>[]
  ) => {
    if (fields) {
      const isValid = await form.trigger(fields);

      return isValid;
    }

    return false;
  };

  const email = form.watch("email");
  const isRootUser = form.watch("isRootUser");

  const handleResendOtp = async () => {
    const fieldIsValidated = await validateSelectedField(["email"]);
    if (fieldIsValidated) {
      const res = await resendAuthOtp({
        otpRef,
      }).unwrap();

      if (res.success) {
        toast.success(`An OTP has been sent your email "${email}"`);
        setCooldown(60);
      }
    }
  };

  const handleForgotPassword = async () => {
    const fieldIsValidated = await validateSelectedField(["email"]);
    if (fieldIsValidated) {
      const res = await initiateForgotPassword({
        email,
        accountId: isRootUser ? accountData?.id : undefined,
        companyId: isRootUser ? undefined : cid || data?.id,
      }).unwrap();

      if (res.success) {
        toast.success(`An OTP has been sent your email "${email}"`);
        setOtpRef(res.data.otpRef);
        setCooldown(60);
        setCurrentStep(1);
      }
    }
  };

  const handleCompleteForgotPassword = async () => {
    const fieldIsValidated = await validateSelectedField([
      "email",
      "otp",
      "user",
    ]);
    if (fieldIsValidated) {
      const { email, otp, user } = form.getValues();
      const res = await completeForgotPassword({
        email,
        accountId: isRootUser ? accountData?.id : undefined,
        otp,
        password: user.password,
        companyId: isRootUser ? undefined : cid || data?.id,
      }).unwrap();

      if (res.success) {
        toast.success(`Password changed successfully. Proceeding to login`);
        navigate(`${RouteConstant.auth.login}?cid=${cid || data?.id}`);
      }
    }
  };
  console.log(form.formState.errors);

  return (
    <>
      <section className="max-w-md mx-auto min-h-screen place-content-center">
        <AuthFormHeader
          title={headers[currentStep].title}
          description={headers[currentStep].description}
          showImg={false}
        />

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 bg-white shadow-md border rounded-md p-8"
          >
            {currentStep === 0 && (
              <div className="space-y-4">
                <InputField
                  form={form}
                  name="email"
                  label="Email"
                  placeholder="Enter account email"
                />

                <CheckBoxField
                  form={form}
                  label="Is root user?"
                  name="isRootUser"
                />

                <Button
                  type="button"
                  className="w-full font-medium"
                  disabled={isLoading}
                  onClick={handleForgotPassword}
                >
                  {isLoading && <LoaderIcon className="animate-spin" />}
                  Proceed
                </Button>
              </div>
            )}

            {currentStep === 1 && (
              <div className="space-y-4">
                <InputField
                  form={form}
                  name="user.password"
                  label="Password"
                  type="password"
                  placeholder="••••••••"
                />
                <InputField
                  form={form}
                  name="user.confirmPassword"
                  label="Confirm Password"
                  type="password"
                  placeholder="Enter password again"
                />
                <OtpInputField
                  form={form}
                  name="otp"
                  label="OTP"
                  inputMode="numeric"
                />
                <Button
                  variant="link"
                  type="button"
                  className="text-blue-600 text-sm px-0"
                  onClick={handleResendOtp}
                  disabled={isResendingAuthOtp || cooldown > 0}
                >
                  {isResendingAuthOtp
                    ? "Resending..."
                    : cooldown > 0
                    ? `Resend OTP in ${cooldown}s`
                    : "Resend OTP"}
                </Button>
                <Button
                  className="w-full font-medium"
                  disabled={isCompletingForgotPassword}
                  type="button"
                  onClick={handleCompleteForgotPassword}
                >
                  {isCompletingForgotPassword && (
                    <LoaderIcon className="animate-spin" />
                  )}
                  Reset Password
                </Button>
                <Button
                  className="w-full font-medium"
                  disabled={isCompletingForgotPassword}
                  type="button"
                  onClick={() => setCurrentStep(0)}
                  variant="outline"
                >
                  Go Back
                </Button>
              </div>
            )}
          </form>
        </Form>
      </section>
    </>
  );
};

export default ForgotPasswordForm;
