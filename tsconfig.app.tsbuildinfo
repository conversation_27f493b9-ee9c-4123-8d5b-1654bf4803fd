{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/payebreakdown.tsx", "./src/components/analysis/payrollprocessed.tsx", "./src/components/analysis/taxanddeductions.tsx", "./src/components/analysis/totalbranches.tsx", "./src/components/analysis/totalemployee.tsx", "./src/components/cards/productcard.tsx", "./src/components/cards/promocard.tsx", "./src/components/dropdown/companydropdown.tsx", "./src/components/form-fields/checkboxfield.tsx", "./src/components/form-fields/comboboxfield.tsx", "./src/components/form-fields/datefield.tsx", "./src/components/form-fields/documentdropzonefield.tsx", "./src/components/form-fields/inputfield.tsx", "./src/components/form-fields/multiselectfield.tsx", "./src/components/form-fields/otpinputfield.tsx", "./src/components/form-fields/phoneinputfield.tsx", "./src/components/form-fields/radiogroupfield.tsx", "./src/components/form-fields/textareafield.tsx", "./src/components/form-fields/index.ts", "./src/components/forms/changepasswordform.tsx", "./src/components/forms/createallowanceform.tsx.tsx", "./src/components/forms/createbranchform.tsx", "./src/components/forms/createcompanyform.tsx", "./src/components/forms/createcontracttypeform.tsx", "./src/components/forms/createdeductionform.tsx", "./src/components/forms/createdepartmentform.tsx", "./src/components/forms/createdesignationform.tsx", "./src/components/forms/createemployeeform.tsx", "./src/components/forms/creategradeform.tsx", "./src/components/forms/creategradelevelform.tsx", "./src/components/forms/createnewtaxjurisdictionform.tsx", "./src/components/forms/createregionform.tsx", "./src/components/forms/createroleform.tsx", "./src/components/forms/createsalarypackageform.tsx", "./src/components/forms/createsubbranchform.tsx", "./src/components/forms/createunitform.tsx", "./src/components/forms/createuserform.tsx", "./src/components/forms/forgotpasswordform.tsx", "./src/components/forms/iamloginform.tsx", "./src/components/forms/loginform.tsx", "./src/components/forms/onboardingform.tsx", "./src/components/forms/rootloginform.tsx", "./src/components/forms/create/createemployeegroupform.tsx", "./src/components/forms/update/updatebranchform.tsx", "./src/components/forms/update/updatecontracttypeform.tsx", "./src/components/forms/update/updatedepartmentform.tsx", "./src/components/forms/update/updatedesignationform.tsx", "./src/components/forms/update/updategradeform.tsx", "./src/components/forms/update/updategradelevelform.tsx", "./src/components/forms/update/updateroleform.tsx", "./src/components/forms/update/updateuserform.tsx", "./src/components/headers/employeetableheaderactions.tsx", "./src/components/headers/sectionheader.tsx", "./src/components/headers/taxjurisdictiontableheaderactions.tsx", "./src/components/layout/appsidebar.tsx", "./src/components/layout/navbar.tsx", "./src/components/layout/sidebartoggler.tsx", "./src/components/layout/index.ts", "./src/components/modal/accountcreatedsuccessmodal.tsx", "./src/components/modal/allowancemodal.tsx", "./src/components/modal/batchrecordmodal.tsx", "./src/components/modal/changepasswordmodal.tsx", "./src/components/modal/confirmationmodal.tsx", "./src/components/modal/contracttypemodal.tsx", "./src/components/modal/createbranchmodal.tsx", "./src/components/modal/createemployeegroupmodal.tsx", "./src/components/modal/createemployeemodal.tsx", "./src/components/modal/createrolemodal.tsx", "./src/components/modal/cropmodal.tsx", "./src/components/modal/deductionmodal.tsx", "./src/components/modal/deleteconfirmatonmodal.tsx", "./src/components/modal/departmentmodal.tsx", "./src/components/modal/designationmodal.tsx", "./src/components/modal/employeebulkuploadmodal-prev.tsx", "./src/components/modal/employeebulkuploadmodal.tsx", "./src/components/modal/grademodal.tsx", "./src/components/modal/onboardingotpmodal.tsx", "./src/components/modal/payrolluploadmodal.tsx", "./src/components/modal/regionmodal.tsx", "./src/components/modal/salarypackagemodal.tsx", "./src/components/modal/sessionexpiredmodal.tsx", "./src/components/modal/subbranchmodal.tsx", "./src/components/modal/taxjurisdictionmodal.tsx", "./src/components/modal/taxjurisdictionuploadmodal.tsx", "./src/components/modal/unitmodal.tsx", "./src/components/modal/usermodal.tsx", "./src/components/modal/index.ts", "./src/components/nav/bottomnavbar.tsx", "./src/components/nav/mobilemenu.tsx", "./src/components/nav/navbar.tsx", "./src/components/providers/authprovider.tsx", "./src/components/providers/modalprovider.tsx", "./src/components/providers/sheetprovider.tsx", "./src/components/providers/storeprovider.tsx", "./src/components/sections/authformheader.tsx", "./src/components/sections/markethotproducts.tsx", "./src/components/sections/newlyarrived.tsx", "./src/components/sections/populardeals.tsx", "./src/components/sections/specialdeals.tsx", "./src/components/sections/topdeals.tsx", "./src/components/sections/topsearchedproducts.tsx", "./src/components/shared/collapsibleitem.tsx", "./src/components/shared/hero.tsx", "./src/components/shared/logo.tsx", "./src/components/shared/protectedroute.tsx", "./src/components/shared/rating.tsx", "./src/components/shared/phone-input/index.tsx", "./src/components/shared/phone-input/use-state-history.tsx", "./src/components/sheets/authorizationsheet.tsx", "./src/components/sheets/batchrecordsheet.tsx", "./src/components/sheets/createcompanysheet.tsx", "./src/components/sheets/entityviewsheet.tsx", "./src/components/sheets/jsonsheet.tsx", "./src/components/sheets/payrollbatchsheet.tsx", "./src/components/sheets/updatebranchsheet.tsx", "./src/components/sheets/updatecontracttypesheet.tsx", "./src/components/sheets/updatedepartmentsheet.tsx", "./src/components/sheets/updatedesignationsheet.tsx", "./src/components/sheets/updategradelevelsheet.tsx", "./src/components/sheets/updategradesheet.tsx", "./src/components/sheets/updaterolesheet.tsx", "./src/components/sheets/updateusersheet.tsx", "./src/components/sheets/index.ts", "./src/components/table/authorizationqueuetable.tsx", "./src/components/table/basetable.tsx", "./src/components/table/batchrecordtable.tsx", "./src/components/table/batchtable.tsx", "./src/components/table/branchtable.tsx", "./src/components/table/contracttypetable.tsx", "./src/components/table/departmenttable.tsx", "./src/components/table/designationtable.tsx", "./src/components/table/employeegrouptable.tsx", "./src/components/table/employeetable.tsx", "./src/components/table/gradetable.tsx", "./src/components/table/payrollbatchtable.tsx", "./src/components/table/payrolltable.tsx", "./src/components/table/privilegetable.tsx", "./src/components/table/regiontable.tsx", "./src/components/table/roletable.tsx", "./src/components/table/salarypackagetable.tsx", "./src/components/table/statetable.tsx", "./src/components/table/subbranchtable.tsx", "./src/components/table/unittable.tsx", "./src/components/table/usertable.tsx", "./src/components/table/_components/defaultheader.tsx", "./src/components/table/_components/tablepagination.tsx", "./src/components/table/renderer/actioncellrenderer .tsx", "./src/components/table/renderer/iamuseractioncellrenderer.tsx", "./src/components/table/renderer/productcellrenderer.tsx", "./src/components/table/renderer/statuscellrenderer.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/tooltip.tsx", "./src/config/app.config.ts", "./src/constants/deleteentityconstant.ts", "./src/constants/employeefieldmappingconstant.ts", "./src/constants/menuconstants.ts", "./src/constants/modalconstant.ts", "./src/constants/payrollbatchtablecolumnconstant.ts", "./src/constants/payrollfieldmappingconstant.ts", "./src/constants/routeconstant.ts", "./src/constants/sheetconstant.ts", "./src/context/auth.context.ts", "./src/data/countries.ts", "./src/data/currencies.ts", "./src/data/industries.ts", "./src/data/industry-type.ts", "./src/data/state-lga.ts", "./src/hooks/index.ts", "./src/hooks/use-mobile.ts", "./src/hooks/useauth.tsx", "./src/lib/calculatepayetax.ts", "./src/lib/getcroppedimg.ts", "./src/lib/uploadfiletocloudinary.ts", "./src/lib/utils.ts", "./src/models/request/allowance/create-allowance.request.ts", "./src/models/request/auth/change-password.request.ts", "./src/models/request/auth/complete-forgot-password.request.ts", "./src/models/request/auth/initiate-forgot-password.request.ts", "./src/models/request/auth/login.request.ts", "./src/models/request/company/create-company.request.ts", "./src/models/request/employee/create-employee.request.ts", "./src/models/request/employee-group/create-employee-group.request.ts", "./src/models/request/enrollment/complete-enrollment.ts", "./src/models/request/enrollment/initiate-enrollment.request.ts", "./src/models/request/kyc/verify-bvn.request.ts", "./src/models/request/kyc/verify-nin.request.ts", "./src/models/request/role/create-role.request.ts", "./src/models/request/role/update-role.request.ts", "./src/models/request/salary-package/create-salary-package.request.ts", "./src/models/response/default.response.ts", "./src/models/response/initiate-enrollment.response.ts", "./src/models/response/account/account.response.ts", "./src/models/response/allowances/index.ts", "./src/models/response/auth/login.response.ts", "./src/models/response/authorization/index.ts", "./src/models/response/branch/index.ts", "./src/models/response/company/company.response.ts", "./src/models/response/contract-type/index.ts", "./src/models/response/dashboard/index.ts", "./src/models/response/department/index.ts", "./src/models/response/designation/index.ts", "./src/models/response/emp-batch/index.ts", "./src/models/response/employee/index.ts", "./src/models/response/grade/index.ts", "./src/models/response/grade-level/index.ts", "./src/models/response/kyc/verify-bvn.response.ts", "./src/models/response/kyc/verify-nin.response.ts", "./src/models/response/locations/index.ts", "./src/models/response/payroll/read-payroll-batch.response.ts", "./src/models/response/payroll/read-payroll.response.ts", "./src/models/response/privilege/index.ts", "./src/models/response/role/index.ts", "./src/models/response/salary-package/index.ts", "./src/models/response/sub-branch/index.ts", "./src/models/response/unit/index.ts", "./src/models/response/users/index.ts", "./src/models/validations/index.ts", "./src/models/validations/allowance/create-allowance.validation.ts", "./src/models/validations/auth/change-password.validation.ts", "./src/models/validations/auth/forgot-password.validation.ts", "./src/models/validations/auth/login.validation.ts", "./src/models/validations/auth/onboarding.validation.ts", "./src/models/validations/branch/create-branch.validation.ts", "./src/models/validations/branch/delete-branch.validation.ts", "./src/models/validations/branch/update-branch.validation.ts", "./src/models/validations/company/create-company.validation.ts", "./src/models/validations/contract-type/update-contract-type.validation.ts", "./src/models/validations/department/create-department.validation.ts", "./src/models/validations/department/update-department.validation.ts", "./src/models/validations/designation/update-designation.validation.ts", "./src/models/validations/employee/create-bulk-employee.validation.ts", "./src/models/validations/employee/create-employee.validation.ts", "./src/models/validations/employee-group/create-employee-group.validation.ts", "./src/models/validations/grade/create-grade.validation.ts", "./src/models/validations/grade/update-grade.validation.ts", "./src/models/validations/grade-level/create-grade-level.validation.ts", "./src/models/validations/grade-level/update-grade-level.validation.ts", "./src/models/validations/onboarding/complete-onboarding.validation.ts", "./src/models/validations/payroll/create-payroll-record.validation.ts", "./src/models/validations/payroll/create-payroll-upload.validation.ts", "./src/models/validations/role/create-role.validation.ts", "./src/models/validations/role/update-role.validation.ts", "./src/models/validations/salary-package/create-salary-package.validation.ts", "./src/models/validations/sub-branch/create-sub-branch.validation.ts", "./src/models/validations/unit/create-unit.validation.ts", "./src/models/validations/user/create-user.validation.ts", "./src/models/validations/user/update-user.validation.ts", "./src/routes/index.tsx", "./src/routes/routes/auth.route.ts", "./src/routes/routes/dasboard.route.ts", "./src/routes/routes/index.ts", "./src/services/account.service.ts", "./src/services/allowance.service.ts", "./src/services/auth.service.ts", "./src/services/authorization.service.ts", "./src/services/base.service.ts", "./src/services/branch.service.ts", "./src/services/company.service.ts", "./src/services/contract-type.service.ts", "./src/services/dashboard.service.ts", "./src/services/deduction.service.ts", "./src/services/department.service.ts", "./src/services/designation.service.ts", "./src/services/employee-batch.service.ts", "./src/services/employee-group.service.ts", "./src/services/employee.service.ts", "./src/services/enrollment.service.ts", "./src/services/grade.service.ts", "./src/services/kyc.service.ts", "./src/services/location.service.ts", "./src/services/payroll.service.ts", "./src/services/privilege.service.ts", "./src/services/region.service.ts", "./src/services/role.service.ts", "./src/services/salary-package.service.ts", "./src/services/sub-branch.service.ts", "./src/services/tax-jurisdiction.service.ts", "./src/services/unit.service.ts", "./src/services/user.service.ts", "./src/store/index.ts", "./src/store/module/account.ts", "./src/store/module/auth.ts", "./src/store/module/company.ts", "./src/store/module/index.ts", "./src/store/module/modal.ts", "./src/store/module/sheet.ts", "./src/view/authorizationqueueview.tsx", "./src/view/batchview.tsx", "./src/view/branchview.tsx", "./src/view/contracttypeview.tsx", "./src/view/dashboardview.tsx", "./src/view/departmentview.tsx", "./src/view/designationview.tsx", "./src/view/employeegroupview.tsx", "./src/view/employeeview.tsx", "./src/view/gradeview.tsx", "./src/view/payrollview.tsx", "./src/view/regionview.tsx", "./src/view/roleview.tsx", "./src/view/salarypackageview.tsx", "./src/view/subbranchview.tsx", "./src/view/taxjurisdictionview.tsx", "./src/view/unitview.tsx", "./src/view/userview.tsx", "./src/view/index.ts", "./src/view/auth/forgotpasswordview.tsx", "./src/view/auth/loginview.tsx", "./src/view/auth/newpasswordview.tsx", "./src/view/auth/onboardingview.tsx", "./src/view/auth/index.ts", "./src/view/layout/authlayout.tsx", "./src/view/layout/dashboardlayout.tsx"], "errors": true, "version": "5.7.3"}