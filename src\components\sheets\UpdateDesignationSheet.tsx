"use client";

import { useAppSelector } from "@/hooks";
import { DesignationResponseDef } from "@/models/response/designation";
import UpdateDesignationForm from "../forms/update/UpdateDesignationForm";
import { DialogDescription } from "../ui/dialog";
import { She<PERSON><PERSON>eader, SheetTitle } from "../ui/sheet";

const UpdateDesignationSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: DesignationResponseDef };
  return (
    <>
      <SheetHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <SheetTitle className="">Update designation</SheetTitle>
        <DialogDescription className="max-w-sm">{`Update "${data.name}" designation`}</DialogDescription>
      </SheetHeader>
      <UpdateDesignationForm />
    </>
  );
};

export default UpdateDesignationSheet;
