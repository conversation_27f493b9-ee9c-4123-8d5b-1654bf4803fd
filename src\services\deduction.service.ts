// services/authApi.ts

import { CreateAllowanceRequestDef } from "@/models/request/allowance/create-allowance.request";
import { DefaultResponse } from "@/models/response/default.response";
import { UnitResponseDef } from "@/models/response/unit";
import { baseService } from "./base.service";

export const allowanceService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createDeduction: builder.mutation<
      DefaultResponse,
      CreateAllowanceRequestDef
    >({
      query: (credentials) => ({
        url: "/deduction/create",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Deductions"],
    }),
    getDeductions: builder.query<
      DefaultResponse & { data: UnitResponseDef[] },
      void
    >({
      query: () => ({
        url: "/deduction/read",
        method: "GET",
      }),
      providesTags: ["Deductions"],
    }),
  }),
  overrideExisting: false,
});

export const { useCreateDeductionMutation, useGetDeductionsQuery } =
  allowanceService;
