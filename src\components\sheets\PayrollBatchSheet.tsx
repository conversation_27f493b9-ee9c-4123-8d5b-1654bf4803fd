"use client";

import { useAppSelector } from "@/hooks";
import { PayrollUploadResponseDef } from "@/models/response/payroll/read-payroll.response";
import PayrollBatchTable from "../table/PayrollBatchTable";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";
import { SheetDescription, SheetHeader, SheetTitle } from "../ui/sheet";

const BatchRecordSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: PayrollUploadResponseDef };
  return (
    <>
      <SheetHeader>
        <div className="space-y-2">
          <SheetTitle className="font-medium text-left !text-base">
            {`Payroll Batch Records Details - ${data.period}`}
          </SheetTitle>
          <SheetDescription className="font-medium text-left !text-xs uppercase">
            {`PERIOD: ${data.period}`}
          </SheetDescription>
        </div>
      </SheetHeader>
      <ScrollArea className="h-[calc(100%-5rem)] border-t p-5">
        <PayrollBatchTable />
        <ScrollBar />
      </ScrollArea>
    </>
  );
};

export default BatchRecordSheet;
