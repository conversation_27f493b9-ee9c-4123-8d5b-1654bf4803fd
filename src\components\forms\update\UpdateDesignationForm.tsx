import { Input<PERSON>ield, TextareaField } from "@/components/form-fields";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { DesignationResponseDef } from "@/models/response/designation";
import {
  UpdateDesignationDef,
  UpdateDesignationSchema,
} from "@/models/validations/designation/update-designation.validation";
import { useUpdateDesignationMutation } from "@/services/designation.service";
import { sheet } from "@/store/module/sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const UpdateDesignationForm = () => {
  const dispatch = useAppDispatch();

  const [updateDesignation, { isLoading: isCreatingRole, error, requestId }] =
    useUpdateDesignationMutation();

  const {
    sheetOptions: { metadata: sheetMetadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = sheetMetadata as { data: DesignationResponseDef };

  const form = useForm<UpdateDesignationDef>({
    resolver: zodResolver(UpdateDesignationSchema),
    defaultValues: {
      description: data.description,
      id: data.id,
      name: data.name,
    },
    mode: "all",
  });

  const onSubmit = async (data: UpdateDesignationDef) => {
    try {
      const res = await updateDesignation(data).unwrap();

      if (res.success) {
        toast("Request successful and pending authorization");
        dispatch(sheet.mutation.close());
      }
    } catch {
      if (error && typeof error === "object" && "data" in error) {
        const errData = error.data as { message?: string };
        toast(errData.message ?? "Something went wrong.", { id: requestId });
      } else {
        toast("Something went wrong. Try again!", { id: requestId });
      }
    }
  };

  return (
    <section className=" p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="name"
              label="Title"
              placeholder="Enter designation title"
            />
            <TextareaField
              form={form}
              name="description"
              label="Description "
              placeholder="Enter designation description"
            />
          </div>
          <Button className="w-full font-medium" disabled={isCreatingRole}>
            {isCreatingRole && <LoaderIcon className="animate-spin" />}
            Update Designation
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default UpdateDesignationForm;
