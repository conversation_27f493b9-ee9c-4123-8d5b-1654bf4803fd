// services/authApi.ts

import { CreateAllowanceRequestDef } from "@/models/request/allowance/create-allowance.request";
import { AllowanceResponseDef } from "@/models/response/allowances";
import { DefaultResponse } from "@/models/response/default.response";
import { baseService } from "./base.service";

export const allowanceService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createAllowance: builder.mutation<
      DefaultResponse,
      CreateAllowanceRequestDef
    >({
      query: (credentials) => ({
        url: "/allowance/create",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Allowances"],
    }),
    getAllowances: builder.query<
      DefaultResponse & { data: AllowanceResponseDef[] },
      void
    >({
      query: () => ({
        url: "/allowance/read",
        method: "GET",
      }),
      providesTags: ["Allowances"],
    }),
    getAllowancesByGradeLevelId: builder.query<
      DefaultResponse & { data: AllowanceResponseDef[] },
      { gradeLevelId: string }
    >({
      query: (payload) => ({
        url: `allowance/read/by-grade-level/${payload.gradeLevelId}`,
        method: "GET",
      }),
      providesTags: (__, _, arg) => [
        {
          type: "Allowances",
          id: `${arg.gradeLevelId}`,
        },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateAllowanceMutation,
  useGetAllowancesQuery,
  useGetAllowancesByGradeLevelIdQuery,
} = allowanceService;
