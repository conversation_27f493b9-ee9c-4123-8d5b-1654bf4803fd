import { Link } from "react-router-dom";
type PromoCardProps = {
  promo: {
    name: string;
    image: string;
  };
};
const PromoCard = ({ promo: { image, name } }: PromoCardProps) => {
  return (
    <Link
      to="/"
      className="flex-1 flex flex-col transition-all duration-500 justify-center items-center pb-2 min-w-[171px] hover:shadow-md hover:rounded-md border border-transparent hover:border-border bg-white overflow-clip"
    >
      <img
        src={image}
        alt="Bread"
        className="w-[155.34px] h-[143.59px] object-cover mb-3"
      />
      <p className="text-center text-sm text-black">{name}</p>
    </Link>
  );
};
export default PromoCard;
