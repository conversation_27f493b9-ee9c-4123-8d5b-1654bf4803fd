import { AuthContext } from "@/context/auth.context";
import { PropsWithChildren, useState } from "react";

type AuthProviderProps = PropsWithChildren & {
  isSignedIn?: boolean;
};

export default function AuthProvider({
  children,
  isSignedIn,
}: AuthProviderProps) {
  const [user] = useState<{ name: string } | null>(
    isSignedIn ? { name: "Adams" } : null
  );

  return <AuthContext.Provider value={user}>{children}</AuthContext.Provider>;
}
