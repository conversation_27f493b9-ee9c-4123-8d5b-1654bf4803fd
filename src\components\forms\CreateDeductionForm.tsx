import { useAppDispatch, useAppSelector } from "@/hooks";
import { CreateAllowanceRequestDef } from "@/models/request/allowance/create-allowance.request";
import { GradeLevelResponseDef } from "@/models/response/grade-level";
import {
  CreateAllowanceDef,
  CreateAllowanceSchema,
} from "@/models/validations/allowance/create-allowance.validation";
import { useCreateDeductionMutation } from "@/services/deduction.service";
import { useGetGradeLevelsQuery } from "@/services/employee-group.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { InputField, TextareaField } from "../form-fields";
import ComboboxField from "../form-fields/ComboBoxField";
import { But<PERSON> } from "../ui/button";
import { Form } from "../ui/form";

const CreateDeductionForm = () => {
  const dispatch = useAppDispatch();

  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);

  const {
    data: { id },
  } = metadata as { data: GradeLevelResponseDef };

  const { data: response, isLoading } = useGetGradeLevelsQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const [createDeduction, { isLoading: isCreatingEmployee, error }] =
    useCreateDeductionMutation();

  const form = useForm<CreateAllowanceDef>({
    resolver: zodResolver(CreateAllowanceSchema),
    defaultValues: {
      gradeLevelId: id,
    },
    mode: "all",
  });

  const onSubmit = async (payload: CreateAllowanceDef) => {
    const data: CreateAllowanceRequestDef = {
      ...payload,
      description: payload.description || payload.name,
      amount: Number(payload.amount.replaceAll(",", "")),
    };
    try {
      const res = await createDeduction(data).unwrap();

      if (res.success) {
        toast(
          "Deduction creation request submitted and is pending authorization"
        );
        dispatch(modal.mutation.close());
      }
    } catch {
      if (error && typeof error === "object" && "data" in error) {
        const errData = error.data as { message?: string };
        toast(errData.message ?? "Something went wrong.");
      } else {
        toast("Something went wrong. Try again!");
      }

      // toast(error)
    }
  };

  console.log(form.formState.errors);

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <>
              <InputField
                form={form}
                name="name"
                label="Deduction name"
                placeholder="Staff Welfare"
              />
              <ComboboxField
                options={
                  response
                    ? response?.data.map((glevel) => ({
                        label: glevel.name,
                        value: glevel.id,
                      }))
                    : []
                }
                labelKey="label"
                valueKey="value"
                form={form}
                name="gradeLevelId"
                label="Select Grade Level"
                placeholder="Select grade level"
                isLoading={isLoading}
              />

              <InputField
                form={form}
                name="amount"
                label="Amount"
                placeholder="Enter amount"
                formatAsCurrency
              />

              <TextareaField
                form={form}
                name="description"
                label="Description "
                placeholder="Enter unit description"
              />

              <div className="flex gap-4 justify-between">
                <Button className="font-medium" disabled={isCreatingEmployee}>
                  {isCreatingEmployee && (
                    <LoaderIcon className="animate-spin" />
                  )}
                  Create new deduction
                </Button>
              </div>
            </>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default CreateDeductionForm;
