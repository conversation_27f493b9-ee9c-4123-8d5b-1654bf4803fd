import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import React from "react";
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "../ui/input-otp";

// Omit both 'name' and 'form' from the base input props
interface OtpInputFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  name: FieldPath<T>;
  label?: string;
  description?: string | React.ReactNode;
  showDescriptionAfterLabel?: boolean;
  maxLength?: number;
  className?: string;
  inputMode?:
    | "search"
    | "text"
    | "none"
    | "tel"
    | "url"
    | "email"
    | "numeric"
    | "decimal";
}

const OtpInputField = <T extends FieldValues>({
  form,
  name,
  label,
  description,
  maxLength,
  className,
  inputMode = "numeric",
}: OtpInputFieldProps<T>) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className="w-full">
          {label && <FormLabel className="font-medium">{label}</FormLabel>}

          <FormControl>
            <InputOTP
              maxLength={maxLength || 6}
              {...field}
              inputMode={inputMode}
              onChange={(val) => {
                // Optional: strip non-digit characters if needed
                const digitsOnly = val.replace(/\D/g, "");
                field.onChange(digitsOnly);
              }}
            >
              <InputOTPGroup inputMode="numeric">
                {Array.from({ length: maxLength || 6 }).map((_, index) => (
                  <InputOTPSlot
                    index={index}
                    key={index}
                    className={className}
                  />
                ))}
              </InputOTPGroup>
            </InputOTP>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

OtpInputField.displayName = "OtpInputField";

export default OtpInputField;
