import { z } from "zod";

export const RootLoginSchema = z.object({
  email: z
    .string()
    .email({ message: "Please enter a valid email address." })
    .trim(),
  password: z
    .string()
});

export type RootLoginDef = z.infer<typeof RootLoginSchema>;

export const IamLoginSchema = z.object({
  email: z
    .string()
    .email({ message: "Please enter a valid email address." })
    .trim(),
  password: z
    .string(),
  accountId: z.string().min(1, "Required"),
});

export type IamLoginDef = z.infer<typeof IamLoginSchema>;
