import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { SubBrancheResponseDef } from "@/models/response/sub-branch";
import { UnitResponseDef } from "@/models/response/unit";
import { useGetSubBranchesQuery } from "@/services/sub-branch.service";
import { sheet } from "@/store/module/sheet";
import { ColDef, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import BaseTable from "./BaseTable";

const SubBranchTable = () => {
  const { data: response, isLoading } = useGetSubBranchesQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<SubBrancheResponseDef>[]>([
    {
      headerName: "Name",
      field: "name",
    },
    {
      headerName: "Parent Branch",
      field: "branch",
    },
    {
      headerName: "Description",
      field: "description",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  const onEditClick = (node: IRowNode<UnitResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.entityViewSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={response?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default SubBranchTable;
