import { useAppDispatch, useAppSelector } from "@/hooks";
import { cn } from "@/lib/utils";
import {
  CreateRoleDef,
  CreateRoleSchema,
} from "@/models/validations/role/create-role.validation";
import { useCreateRoleMutation } from "@/services/role.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon, PlusIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { InputField, TextareaField } from "../form-fields";
import PrivilegeTable from "../table/PrivilegeTable";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Form } from "../ui/form";

const CreateRoleForm = () => {
  const dispatch = useAppDispatch();
  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);

  const { selectedPrivilege } = metadata as {
    selectedPrivilege: Array<{
      name: string;
      id: number;
      status: string;
    }>;
  };

  const [open, setOpen] = useState(false);

  const [createRole, { isLoading: isCreatingRole }] =
    useCreateRoleMutation();

  const form = useForm<CreateRoleDef>({
    resolver: zodResolver(CreateRoleSchema),
    mode: "onSubmit",
    // reValidateMode: "onSubmit",
    defaultValues: {
      privileges: [],
    },
  });

  const onSubmit = async (data: CreateRoleDef) => {
    try {
       await createRole(data);
        toast("Role request submitted and is pending authorization");
        dispatch(modal.mutation.close());
    } catch (error: unknown) {
        const errData = error as { data?: { message?: string } };
        toast.error(errData.data?.message ?? "Something went wrong");
    }
  };

  const onClose = () => {
    setOpen(false);
    form.trigger("privileges");
  };

  useEffect(() => {
    if (selectedPrivilege?.length > 0) {
      const privilegesName = selectedPrivilege.map((priv) => priv.name);
      form.setValue("privileges", privilegesName);
    }
  }, [form, selectedPrivilege]);

  const privileges = form.watch("privileges");

  console.log(form.formState.errors);

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="name"
              label="Title"
              placeholder="Enter role title"
            />
            <TextareaField
              form={form}
              name="description"
              label="Description "
              placeholder="Enter role description"
            />
            <Dialog
              open={open}
              onOpenChange={(isOpen) => {
                if (!isOpen) {
                  onClose();
                }
              }}
            >
              <DialogTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className={cn(
                    form.getFieldState("privileges").invalid && "text-red-500"
                  )}
                  onClick={() => setOpen(true)}
                >
                  <PlusIcon />
                  Add Permission
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add privileges</DialogTitle>
                </DialogHeader>
                <div className="">
                  <PrivilegeTable onCancel={onClose} />
                </div>
              </DialogContent>
            </Dialog>
            <p className="text-primary text-xs font-medium">{`${privileges?.length} privileges selected`}</p>
          </div>
          <Button className="w-full font-medium" disabled={isCreatingRole}>
            {isCreatingRole && <LoaderIcon className="animate-spin" />}
            Create Role
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default CreateRoleForm;
