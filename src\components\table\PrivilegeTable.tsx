import { useAppDispatch, useAppSelector } from "@/hooks";
import { cn } from "@/lib/utils";
import { useGetPrivilegesQuery } from "@/services/privilege.service";
import { modal } from "@/store/module/modal";
import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
import { CheckIcon, LoaderCircleIcon } from "lucide-react";
import { useState } from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";

ModuleRegistry.registerModules([AllCommunityModule]);

const PrivilegeTable = ({ onCancel }: { onCancel: () => void }) => {
  const { data: privileges, isLoading } = useGetPrivilegesQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);
  const dispatch = useAppDispatch();

  const { selectedPrivilege } = metadata as {
    selectedPrivilege: Array<{
      name: string;
      id: number;
      status: string;
    }>;
  };

  console.log(selectedPrivilege);

  const [checkedPermissions, setCheckedPermissions] = useState<
    { name: string; id: number; status: string }[]
  >(selectedPrivilege || []);

  const [searchQuery, setSearchQuery] = useState("");

  const toggleCheck = ({
    name,
    id,
    status,
  }: {
    name: string;
    id: number;
    status: string;
  }) => {
    setCheckedPermissions((prev) => {
      const exists = prev.some(({ id: privId }) => privId === id);
      return exists
        ? prev.filter(({ id: privId }) => privId !== id)
        : [
            ...prev,
            {
              id,
              name,
              status,
            },
          ];
    });
  };

  // Function to handle check all/uncheck all
  const toggleCheckAll = () => {
    const filteredPrivilegesData = filteredPrivileges || [];
    const allChecked = filteredPrivilegesData.every(({ id }) =>
      checkedPermissions.some(({ id: privId }) => privId === id)
    );

    if (allChecked) {
      // Uncheck all filtered privileges
      setCheckedPermissions((prev) =>
        prev.filter(
          ({ id }) => !filteredPrivilegesData.some(({ id: filterId }) => filterId === id)
        )
      );
    } else {
      // Check all filtered privileges
      const newPrivileges = filteredPrivilegesData.filter(
        ({ id }) => !checkedPermissions.some(({ id: privId }) => privId === id)
      );
      setCheckedPermissions((prev) => [...prev, ...newPrivileges]);
    }
  };

  const onAdd = () => {
    dispatch(
      modal.mutation.setMetadata({ selectedPrivilege: checkedPermissions })
    );
    onCancel();
  };
  const filteredPrivileges = privileges?.data?.filter(({ name }) =>
    name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Check if all filtered privileges are selected
  const allFilteredChecked = filteredPrivileges && filteredPrivileges.length > 0 && 
    filteredPrivileges.every(({ id }) =>
      checkedPermissions.some(({ id: privId }) => privId === id)
    );

  return (
    <div className="w-full">
      <Input
        type="text"
        placeholder="Search permissions..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        className="mb-4 w-full p-2 border rounded"
      />
      <div className="h-[410px] max-h-[600px] scroll-area ">
        {isLoading ? (
          <LoaderCircleIcon />
        ) : !privileges ? null : (
          <ScrollArea className="h-full ">
            <div className="">
              <div className="bg-gray-100 flex items-center gap-2">
                <Button
                  size="icon"
                  className={cn(
                    "border w-4 h-4 text-center relative flex items-center justify-center p-0 rounded-xs ml-2",
                    allFilteredChecked
                      ? "bg-primary text-white hover:bg-primary"
                      : "bg-white hover:bg-white "
                  )}
                  onClick={toggleCheckAll}
                >
                  <CheckIcon
                    className="!w-3 !h-3 absolute"
                    strokeWidth={4}
                  />
                </Button>
                <p className="font-medium">Check All Permissions</p>
              </div>
              <div>
                {filteredPrivileges?.map(({ id, name, status }) => (
                  <div key={id} className="border-y flex items-center gap-2">
                                        <Button
                      size="icon"
                      className={cn(
                        "border w-4 h-4 text-center relative flex items-center justify-center p-0 rounded-xs ml-2",
                        checkedPermissions.some(
                          ({ id: privId }) => privId === id
                        )
                          ? "bg-primary text-white hover:bg-primary"
                          : "bg-white hover:bg-white "
                      )}
                      onClick={() =>
                        toggleCheck({
                          id,
                          name,
                          status,
                        })
                      }
                    >
                      <CheckIcon
                        className="!w-3 !h-3 absolute"
                        strokeWidth={4}
                      />
                    </Button>
                    <p
                      className={cn(
                        "p-2 text-xs",
                        checkedPermissions.some(
                          ({ id: privId }) => privId === id
                        )
                          ? "text-primary"
                          : "text-neutral-500"
                      )}
                    >
                      {name}
                    </p>
                  </div>
                ))}
              </div>
            </div>
            <ScrollBar orientation="vertical" />
          </ScrollArea>
        )}
      </div>

      <div className="flex flex-row gap-x-3 justify-end mt-5 py-5">
        <Button
          onClick={onCancel}
          variant="ghost"
          className="shadow border border-neutral-100"
        >
          CANCEL
        </Button>
        <Button onClick={onAdd}>ADD</Button>
      </div>
    </div>
  );
};

export default PrivilegeTable;
