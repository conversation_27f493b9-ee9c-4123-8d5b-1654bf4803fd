import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formattedCurrency = (value?: number) => {
  if (value) {
    const num = Number(value.toString().replaceAll(",", "").trim());

    if (isNaN(num)) {
      return value.toString();
    }

    return num === 0
      ? "0"
      : new Intl.NumberFormat("en-NG", {
          style: "currency",
          currency: "NGN",
        }).format(num);
  } else {
    return "0";
  }
};

export const isValidJSON = (str: string) => {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
};
export function camelToSnake(str: string): string {
  return str.replace(/([a-z])([A-Z])/g, "$1_$2").toLowerCase();
}

export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    console.error("Failed to copy: ", err);
    return false;
  }
};

export const formatLabel = (label: string) => {
  return label
    .replace(/([a-z])([A-Z])/g, "$1 $2") // Add space between camelCase
    .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
};

// import { z } from "zod";
// import { CreateEmployeeSchema } from "./path-to-your-schema"; // adjust this path
// import type { CreateEmployeeDef } from "./path-to-your-schema";

// // Utility to convert camelCase to "Title Case"
// const toLabel = (key: string) =>
//   key
//     .replace(/([A-Z])/g, " $1") // insert space before capital letters
//     .replace(/^./, (s) => s.toUpperCase()); // capitalize first letter

// // Extract keys from schema
// const schemaKeys = Object.keys(
//   CreateEmployeeSchema.shape
// ) as (keyof CreateEmployeeDef)[];

// // Generate FIELD_MAPPINGS
// const FIELD_MAPPINGS: { label: string; name: keyof CreateEmployeeDef }[] =
//   schemaKeys.map((key) => ({
//     label: toLabel(key),
//     name: key,
//   }));

// console.log(FIELD_MAPPINGS);
