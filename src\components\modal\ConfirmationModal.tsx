/* eslint-disable @typescript-eslint/no-explicit-any */
import { DeleteEntityConstant } from "@/constants/DeleteEntityConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { useDeleteBranchMutation } from "@/services/branch.service";
import { useDeleteContractTypeMutation } from "@/services/contract-type.service";
import { useDeleteDesignationMutation } from "@/services/designation.service";
import { useDeleteGradeLevelMutation } from "@/services/employee-group.service";
import { useDeleteGradeMutation } from "@/services/grade.service";
import { useDeleteUserMutation } from "@/services/user.service";
import { modal } from "@/store/module/modal";
import { toast } from "sonner";
import DeleteConfirmatonModal from "./DeleteConfirmatonModal";

const ConfirmationModal = () => {
  const dispatch = useAppDispatch();
  const {
    modalOptions: { metadata },
  } = useAppSelector((state) => state.modal);

  const [deleteUser] = useDeleteUserMutation();
  const [deleteBranch] = useDeleteBranchMutation();
  const [deleteGrade] = useDeleteGradeMutation();
  const [deleteGradeLevel] = useDeleteGradeLevelMutation();
  const [deleteDesignation] = useDeleteDesignationMutation();
  const [deleteContractType] = useDeleteContractTypeMutation();

  if (!metadata) return null;

  const { id, entity } = metadata as {
    id: number;
    securityCredentialId?: string;
    entity: DeleteEntityConstant;
    marketplaceId: number;
    marketPlaceTableColumnTableId: number;
  };

  const handleDelete = async (deleteAction: any) => {
    const res = await deleteAction();

    if (res.success) {
      toast.success("Request successful and pending authorztion");
      dispatch(modal.mutation.close());
    }
  };

  const deleteHandlers: Record<string, () => Promise<any>> = {
    [DeleteEntityConstant.users]: () =>
      handleDelete(
        async () => await deleteUser({ id: id.toString() }).unwrap()
      ),
    [DeleteEntityConstant.branch]: () =>
      handleDelete(
        async () => await deleteBranch({ id: id.toString() }).unwrap()
      ),
    [DeleteEntityConstant.grade]: () =>
      handleDelete(
        async () => await deleteGrade({ id: id.toString() }).unwrap()
      ),
    [DeleteEntityConstant.gradeLevel]: () =>
      handleDelete(
        async () => await deleteGradeLevel({ id: id.toString() }).unwrap()
      ),
    [DeleteEntityConstant.designation]: () =>
      handleDelete(
        async () => await deleteDesignation({ id: id.toString() }).unwrap()
      ),
    [DeleteEntityConstant.contractType]: () =>
      handleDelete(
        async () => await deleteContractType({ id: id.toString() }).unwrap()
      ),
  };
  return entity in deleteHandlers ? (
    <DeleteConfirmatonModal handleDelete={deleteHandlers[entity]} />
  ) : null;
};

export default ConfirmationModal;
