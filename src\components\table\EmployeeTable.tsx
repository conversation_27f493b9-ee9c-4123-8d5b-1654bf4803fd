import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { EmployeeResponseDef } from "@/models/response/employee";
import { useGetEmployeesQuery } from "@/services/employee.service";
import { sheet } from "@/store/module/sheet";
import { ColDef, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import BaseTable from "./BaseTable";

const EmployeeTable = () => {
  const { data, isLoading } = useGetEmployeesQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<EmployeeResponseDef>[]>([
    {
      headerName: "Staff Code",
      field: "staffCode",
    },
    {
      headerName: "First Name",
      field: "firstName",
    },
    {
      headerName: "Last Name",
      field: "lastName",
    },
    {
      headerName: "Email",
      field: "email",
    },
    {
      headerName: "Phone Number 1",
      field: "phone1",
    },
    {
      headerName: "Branch / Unit",
      field: "branchName",
    },
    {
      headerName: "Designation",
      field: "jobTitleName",
    },
    {
      headerName: "Department",
      field: "departmentName",
    },
    {
      headerName: "Grade",
      field: "jobGradeName",
    },
    {
      headerName: "Salary Package",
      field: "salaryPackageName",
    },
    {
      headerName: "Grade level",
      field: "gradeLevelName",
    },
    {
      headerName: "Employment Type",
      field: "contractTypeName",
    },
    {
      headerName: "State of orgin",
      field: "stateOfOrigin",
    },
    {
      headerName: "LGA",
      field: "localGovt",
    },
    {
      headerName: "Residential Address",
      field: "residentialAddress",
    },
    {
      headerName: "Residential LGA",
      field: "residentialLocalGovt",
    },
    {
      headerName: "Residential State",
      field: "residentialState",
    },
    {
      headerName: "Residential Country",
      field: "residentialCountry",
    },
    {
      headerName: "PAYE",
      field: "taxId",
    },
    {
      headerName: "Pension ID",
      field: "pensionId",
    },
    {
      headerName: "PFA",
      field: "pfa",
    },

    {
      headerName: "Account Number",
      field: "accountNumber",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  const onEditClick = (node: IRowNode<EmployeeResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.entityViewSheet,
        metadata: {
          title: "Update account information",
          data: node.data,
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={data?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                      bvn: "*********",
                      nin: "***********",
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default EmployeeTable;
