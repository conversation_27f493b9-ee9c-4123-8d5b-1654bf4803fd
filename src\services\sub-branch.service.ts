// services/authApi.ts

import { DefaultResponse } from "@/models/response/default.response";
import { SubBrancheResponseDef } from "@/models/response/sub-branch";
import { CreateSubBranchDef } from "@/models/validations/sub-branch/create-sub-branch.validation";
import { baseService } from "./base.service";

export const subBranchService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createSubBranch: builder.mutation<DefaultResponse, CreateSubBranchDef>({
      query: (credentials) => ({
        url: "/sub-branch/create",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["SubBranches"],
    }),
    getSubBranches: builder.query<
      DefaultResponse & { data: SubBrancheResponseDef[] },
      void
    >({
      query: () => ({
        url: "/sub-branch/read",
        method: "GET",
      }),
      providesTags: ["SubBranches"],
    }),
  }),
  overrideExisting: false,
});

export const { useCreateSubBranchMutation, useGetSubBranchesQuery } =
  subBranchService;
