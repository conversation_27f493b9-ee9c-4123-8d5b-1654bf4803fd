import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { camelToSnake, cn, copyToClipboard, isValidJSON } from "@/lib/utils";
import { sheet } from "@/store/module/sheet";
import { CheckIcon, CopyIcon, PenIcon } from "lucide-react";
import { useState } from "react";
import React<PERSON>son from "react-json-view";
import { Button } from "../ui/button";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";
import { Sheet<PERSON>eader, SheetTitle } from "../ui/sheet";

const EntityViewSheet = () => {
  const dispatch = useAppDispatch();
  const [copiedKey, setCopiedKey] = useState<string | null>(null);

  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  if (!metadata?.data || metadata?.data === undefined) {
    return (
      <div>
        <p>Data not found</p>
      </div>
    );
  }

  // Function to render specific field values with custom formatting
  const renderFieldValue = (key: string, value: unknown) => {
    // Handle role field - show role name instead of object
    if (key === "role" && value && typeof value === "object" && value !== null && "name" in value) {
      return (value as { name: string }).name;
    }
    
    // Handle branches field - show comma-separated branch names or "All Branches"
    if (key === "branches") {
      const hasAccessToAllBranches = (metadata?.data as { hasAccessToAllBranches?: boolean })?.hasAccessToAllBranches;
      
      if (hasAccessToAllBranches) {
        return "All Branches";
      }
      
      if (value && Array.isArray(value) && value.length > 0) {
        return (value as { name: string; id: string }[]).map((branch) => branch.name).join(", ");
      }
      
      return "No specific branches assigned";
    }
    
    // Handle hasAccessToAllBranches field - skip it since we show this info in branches
    if (key === "hasAccessToAllBranches") {
      return null; // Skip rendering this field
    }
    
    // Default handling for other fields
    if (value !== null) {
      if (key.toLocaleLowerCase().includes("data")) {
        return isValidJSON(value as string) ? (
          <ReactJson
            displayDataTypes={false}
            src={JSON.parse(value as string)}
          />
        ) : (
          value?.toString()
        );
      } else {
        return value?.toString();
      }
    }
    
    return "N/A";
  };

  // Function to get the copy value for clipboard
  const getCopyValue = (key: string, value: unknown) => {
    if (key === "role" && value && typeof value === "object" && value !== null && "name" in value) {
      return (value as { name: string }).name;
    }
    
    if (key === "branches") {
      const hasAccessToAllBranches = (metadata?.data as { hasAccessToAllBranches?: boolean })?.hasAccessToAllBranches;
      
      if (hasAccessToAllBranches) {
        return "All Branches";
      }
      
      if (value && Array.isArray(value) && value.length > 0) {
        return (value as { name: string; id: string }[]).map((branch) => branch.name).join(", ");
      }
      
      return "No specific branches assigned";
    }
    
    return value ? value.toString() : "N/A";
  };

  return (
    <>
      <SheetHeader>
        <div className="flex items-center gap-2">
          <SheetTitle className="font-medium text-left !text-base">
            Details
          </SheetTitle>
          {metadata?.sheetType === SheetConstant.authorizationQueue ? (
            <Button
              size="sm"
              onClick={() =>
                dispatch(
                  sheet.mutation.open({
                    component: SheetConstant.authorizationReviewSheet,
                    metadata: {
                      data: metadata.data,
                    },
                  })
                )
              }
            >
              Review Request
            </Button>
          ) : metadata?.formType ? (
            <Button
              size="icon"
              variant="ghost"
              onClick={() =>
                dispatch(
                  sheet.mutation.open({
                    component: SheetConstant.formSheet,
                    metadata: {
                      formType: metadata?.formType,
                      title: metadata?.title,
                    },
                  })
                )
              }
            >
              <PenIcon />
            </Button>
          ) : null}
        </div>
      </SheetHeader>
      <ScrollArea className="h-[calc(100%-5rem)] border-t">
        <div className="p-4">
          <div className={cn(" gap-5  mt-4 grid")}>
            {Object.entries(metadata?.data)
              .filter(([key]) => key !== "hasAccessToAllBranches") // Skip this field
              .map(([key, value]) => (
              <div
                key={key}
                className={cn(
                  key.toLocaleLowerCase().includes("data") && "col-span-2"
                )}
              >
                <p className="text-xs font-medium text-neutral-500 uppercase">
                  {camelToSnake(key).replaceAll("_", " ")}
                </p>
                <p className={cn("text-sm flex items-center gap-2")}>
                  {renderFieldValue(key, value)}
                  <Button
                    size="icon"
                    variant="ghost"
                    className="w-auto h-auto"
                    onClick={() => {
                      const copyValue = getCopyValue(key, value);
                      copyToClipboard(copyValue);
                      setCopiedKey(key);
                      setTimeout(() => setCopiedKey(null), 2000);
                    }}
                  >
                    {copiedKey === key ? (
                      <CheckIcon className="text-green-600" />
                    ) : (
                      <CopyIcon />
                    )}
                  </Button>
                </p>
              </div>
            ))}
          </div>
        </div>
        <ScrollBar />
      </ScrollArea>
    </>
  );
};

export default EntityViewSheet;
