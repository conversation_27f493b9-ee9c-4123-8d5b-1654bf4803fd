"use client";

import { useAppSelector } from "@/hooks";
import { RoleResponseDef } from "@/models/response/role";
import UpdateDepartmentForm from "../forms/update/UpdateDepartmentForm";
import { DialogDescription } from "../ui/dialog";
import { SheetHeader, SheetTitle } from "../ui/sheet";

const UpdateDepartmentSheet = () => {
  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data } = metadata as { data: RoleResponseDef };
  return (
    <>
      <SheetHeader className="sticky top-0 !bg-white  z-10 flex pt-6">
        <SheetTitle className="">Update department</SheetTitle>
        <DialogDescription className="max-w-sm">{`Update "${data.name}" department`}</DialogDescription>
      </SheetHeader>
      <UpdateDepartmentForm />
    </>
  );
};

export default UpdateDepartmentSheet;
